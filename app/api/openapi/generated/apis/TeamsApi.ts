/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  HTTPValidationError,
  TeamCreate,
  TeamDetailResponse,
  TeamListResponse,
  TeamUpdate,
} from '../models/index';
import {
    HTTPValidationErrorFromJSON,
    HTTPValidationErrorToJSON,
    TeamCreateFromJSON,
    TeamCreateToJSON,
    TeamDetailResponseFromJSON,
    TeamDetailResponseToJSON,
    TeamListResponseFromJSON,
    TeamListResponseToJSON,
    TeamUpdateFromJSON,
    TeamUpdateToJSON,
} from '../models/index';

export interface TeamsCreateTeamRequest {
    teamCreate: TeamCreate;
}

export interface TeamsDeleteTeamRequest {
    teamUuid: string;
}

export interface TeamsGetTeamRequest {
    teamUuid: string;
}

export interface TeamsUpdateTeamRequest {
    teamUuid: string;
    teamUpdate: TeamUpdate;
}

/**
 * 
 */
export class TeamsApi extends runtime.BaseAPI {

    /**
     * Create a new team in the user\'s organization
     * Create team
     */
    async teamsCreateTeamRaw(requestParameters: TeamsCreateTeamRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<TeamDetailResponse>> {
        if (requestParameters['teamCreate'] == null) {
            throw new runtime.RequiredError(
                'teamCreate',
                'Required parameter "teamCreate" was null or undefined when calling teamsCreateTeam().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/team/`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: TeamCreateToJSON(requestParameters['teamCreate']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => TeamDetailResponseFromJSON(jsonValue));
    }

    /**
     * Create a new team in the user\'s organization
     * Create team
     */
    async teamsCreateTeam(requestParameters: TeamsCreateTeamRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<TeamDetailResponse> {
        const response = await this.teamsCreateTeamRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Delete a team (soft delete)
     * Delete team
     */
    async teamsDeleteTeamRaw(requestParameters: TeamsDeleteTeamRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['teamUuid'] == null) {
            throw new runtime.RequiredError(
                'teamUuid',
                'Required parameter "teamUuid" was null or undefined when calling teamsDeleteTeam().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/team/{team_uuid}`.replace(`{${"team_uuid"}}`, encodeURIComponent(String(requestParameters['teamUuid']))),
            method: 'DELETE',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Delete a team (soft delete)
     * Delete team
     */
    async teamsDeleteTeam(requestParameters: TeamsDeleteTeamRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any> {
        const response = await this.teamsDeleteTeamRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Get detailed information about a specific team including members
     * Get team details
     */
    async teamsGetTeamRaw(requestParameters: TeamsGetTeamRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<TeamDetailResponse>> {
        if (requestParameters['teamUuid'] == null) {
            throw new runtime.RequiredError(
                'teamUuid',
                'Required parameter "teamUuid" was null or undefined when calling teamsGetTeam().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/team/{team_uuid}`.replace(`{${"team_uuid"}}`, encodeURIComponent(String(requestParameters['teamUuid']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => TeamDetailResponseFromJSON(jsonValue));
    }

    /**
     * Get detailed information about a specific team including members
     * Get team details
     */
    async teamsGetTeam(requestParameters: TeamsGetTeamRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<TeamDetailResponse> {
        const response = await this.teamsGetTeamRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Get a list of teams in the user\'s organization
     * List teams
     */
    async teamsListTeamsRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<TeamListResponse>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/team/`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => TeamListResponseFromJSON(jsonValue));
    }

    /**
     * Get a list of teams in the user\'s organization
     * List teams
     */
    async teamsListTeams(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<TeamListResponse> {
        const response = await this.teamsListTeamsRaw(initOverrides);
        return await response.value();
    }

    /**
     * Update team information
     * Update team
     */
    async teamsUpdateTeamRaw(requestParameters: TeamsUpdateTeamRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<TeamDetailResponse>> {
        if (requestParameters['teamUuid'] == null) {
            throw new runtime.RequiredError(
                'teamUuid',
                'Required parameter "teamUuid" was null or undefined when calling teamsUpdateTeam().'
            );
        }

        if (requestParameters['teamUpdate'] == null) {
            throw new runtime.RequiredError(
                'teamUpdate',
                'Required parameter "teamUpdate" was null or undefined when calling teamsUpdateTeam().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/team/{team_uuid}`.replace(`{${"team_uuid"}}`, encodeURIComponent(String(requestParameters['teamUuid']))),
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: TeamUpdateToJSON(requestParameters['teamUpdate']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => TeamDetailResponseFromJSON(jsonValue));
    }

    /**
     * Update team information
     * Update team
     */
    async teamsUpdateTeam(requestParameters: TeamsUpdateTeamRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<TeamDetailResponse> {
        const response = await this.teamsUpdateTeamRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
