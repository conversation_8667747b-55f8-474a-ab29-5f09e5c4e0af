/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  AttendeeInfo,
  HTTPValidationError,
  ListAttendeesResponse,
  ListAttendeesUnifiedResponse,
} from '../models/index';
import {
    AttendeeInfoFromJSON,
    AttendeeInfoToJSON,
    HTTPValidationErrorFromJSON,
    HTTPValidationErrorToJSON,
    ListAttendeesResponseFromJSON,
    ListAttendeesResponseToJSON,
    ListAttendeesUnifiedResponseFromJSON,
    ListAttendeesUnifiedResponseToJSON,
} from '../models/index';

export interface AttendeesListAttendeeOptionsRequest {
    userId: string;
    q?: string;
}

export interface AttendeesListAttendeeOptionsUnifiedRequest {
    userId: string;
    q?: string;
    cursor?: string | null;
    pageSize?: number;
    noteId?: string | null;
}

/**
 * 
 */
export class AttendeesApi extends runtime.BaseAPI {

    /**
     * List Attendee Options
     */
    async attendeesListAttendeeOptionsRaw(requestParameters: AttendeesListAttendeeOptionsRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ListAttendeesResponse>> {
        if (requestParameters['userId'] == null) {
            throw new runtime.RequiredError(
                'userId',
                'Required parameter "userId" was null or undefined when calling attendeesListAttendeeOptions().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['q'] != null) {
            queryParameters['q'] = requestParameters['q'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/attendee/attendee_options/{user_id}`.replace(`{${"user_id"}}`, encodeURIComponent(String(requestParameters['userId']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ListAttendeesResponseFromJSON(jsonValue));
    }

    /**
     * List Attendee Options
     */
    async attendeesListAttendeeOptions(requestParameters: AttendeesListAttendeeOptionsRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ListAttendeesResponse> {
        const response = await this.attendeesListAttendeeOptionsRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * List available attendee options (users and clients) for a given user. Users and Clients are both returned.  Args:     user_id: UUID of the user to get attendee options for     q: Search query to filter clients by name     cursor: Pagination cursor for fetching next page of results     page_size: Number of items per page (if not specified, returns all results)     note_id: UUID of the note to filter attendees with (if specified, all attendees already on this note are prepended to the results)     user: Authenticated user making the request  Returns:     ListAttendeesResponse containing users, clients, and pagination metadata
     * List Attendee Options Unified
     */
    async attendeesListAttendeeOptionsUnifiedRaw(requestParameters: AttendeesListAttendeeOptionsUnifiedRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ListAttendeesUnifiedResponse>> {
        if (requestParameters['userId'] == null) {
            throw new runtime.RequiredError(
                'userId',
                'Required parameter "userId" was null or undefined when calling attendeesListAttendeeOptionsUnified().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['q'] != null) {
            queryParameters['q'] = requestParameters['q'];
        }

        if (requestParameters['cursor'] != null) {
            queryParameters['cursor'] = requestParameters['cursor'];
        }

        if (requestParameters['pageSize'] != null) {
            queryParameters['page_size'] = requestParameters['pageSize'];
        }

        if (requestParameters['noteId'] != null) {
            queryParameters['note_id'] = requestParameters['noteId'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/attendee/attendee_options_unified/{user_id}`.replace(`{${"user_id"}}`, encodeURIComponent(String(requestParameters['userId']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ListAttendeesUnifiedResponseFromJSON(jsonValue));
    }

    /**
     * List available attendee options (users and clients) for a given user. Users and Clients are both returned.  Args:     user_id: UUID of the user to get attendee options for     q: Search query to filter clients by name     cursor: Pagination cursor for fetching next page of results     page_size: Number of items per page (if not specified, returns all results)     note_id: UUID of the note to filter attendees with (if specified, all attendees already on this note are prepended to the results)     user: Authenticated user making the request  Returns:     ListAttendeesResponse containing users, clients, and pagination metadata
     * List Attendee Options Unified
     */
    async attendeesListAttendeeOptionsUnified(requestParameters: AttendeesListAttendeeOptionsUnifiedRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ListAttendeesUnifiedResponse> {
        const response = await this.attendeesListAttendeeOptionsUnifiedRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * List Attendees
     */
    async attendeesListAttendeesRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<AttendeeInfo>>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/attendee/`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(AttendeeInfoFromJSON));
    }

    /**
     * List Attendees
     */
    async attendeesListAttendees(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<AttendeeInfo>> {
        const response = await this.attendeesListAttendeesRaw(initOverrides);
        return await response.value();
    }

}
