/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  ApiRoutersUserModelsUserResponse,
  CreateUserRequest,
  CreateUserResponse,
  HTTPValidationError,
  ListUsersResponse,
  UserUpdate,
} from '../models/index';
import {
    ApiRoutersUserModelsUserResponseFromJSON,
    ApiRoutersUserModelsUserResponseToJSON,
    CreateUserRequestFromJSON,
    CreateUserRequestToJSON,
    CreateUserResponseFromJSON,
    CreateUserResponseToJSON,
    HTTPValidationErrorFromJSON,
    HTTPValidationErrorToJSON,
    ListUsersResponseFromJSON,
    ListUsersResponseToJSON,
    UserUpdateFromJSON,
    UserUpdateToJSON,
} from '../models/index';

export interface UserCreateUserRequest {
    createUserRequest: CreateUserRequest;
}

export interface UserEditUserRequest {
    userUuid: string;
    userUpdate: UserUpdate;
}

export interface UserListUsersRequest {
    q?: string | null;
    status?: string | null;
    pageSize?: number | null;
    cursor?: string | null;
}

export interface UserViewUserRequest {
    userUuid: string;
}

/**
 * 
 */
export class UserApi extends runtime.BaseAPI {

    /**
     * Create a user, apply metadata, and optionally send a welcome email. Only users with the \'organization_admin\' entitlement can perform this action.
     * Create a new user in the organization
     */
    async userCreateUserRaw(requestParameters: UserCreateUserRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<CreateUserResponse>> {
        if (requestParameters['createUserRequest'] == null) {
            throw new runtime.RequiredError(
                'createUserRequest',
                'Required parameter "createUserRequest" was null or undefined when calling userCreateUser().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/user/`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: CreateUserRequestToJSON(requestParameters['createUserRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => CreateUserResponseFromJSON(jsonValue));
    }

    /**
     * Create a user, apply metadata, and optionally send a welcome email. Only users with the \'organization_admin\' entitlement can perform this action.
     * Create a new user in the organization
     */
    async userCreateUser(requestParameters: UserCreateUserRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<CreateUserResponse> {
        const response = await this.userCreateUserRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Only super users and org admin can perform this action.
     * Update user data
     */
    async userEditUserRaw(requestParameters: UserEditUserRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['userUuid'] == null) {
            throw new runtime.RequiredError(
                'userUuid',
                'Required parameter "userUuid" was null or undefined when calling userEditUser().'
            );
        }

        if (requestParameters['userUpdate'] == null) {
            throw new runtime.RequiredError(
                'userUpdate',
                'Required parameter "userUpdate" was null or undefined when calling userEditUser().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/user/{user_uuid}`.replace(`{${"user_uuid"}}`, encodeURIComponent(String(requestParameters['userUuid']))),
            method: 'PATCH',
            headers: headerParameters,
            query: queryParameters,
            body: UserUpdateToJSON(requestParameters['userUpdate']),
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Only super users and org admin can perform this action.
     * Update user data
     */
    async userEditUser(requestParameters: UserEditUserRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any> {
        const response = await this.userEditUserRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * List users with optional filters and pagination.
     * List users in the organization
     */
    async userListUsersRaw(requestParameters: UserListUsersRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ListUsersResponse>> {
        const queryParameters: any = {};

        if (requestParameters['q'] != null) {
            queryParameters['q'] = requestParameters['q'];
        }

        if (requestParameters['status'] != null) {
            queryParameters['status'] = requestParameters['status'];
        }

        if (requestParameters['pageSize'] != null) {
            queryParameters['page_size'] = requestParameters['pageSize'];
        }

        if (requestParameters['cursor'] != null) {
            queryParameters['cursor'] = requestParameters['cursor'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/user/`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ListUsersResponseFromJSON(jsonValue));
    }

    /**
     * List users with optional filters and pagination.
     * List users in the organization
     */
    async userListUsers(requestParameters: UserListUsersRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ListUsersResponse> {
        const response = await this.userListUsersRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Fetch user details by UUID.
     * Get user detail
     */
    async userViewUserRaw(requestParameters: UserViewUserRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ApiRoutersUserModelsUserResponse>> {
        if (requestParameters['userUuid'] == null) {
            throw new runtime.RequiredError(
                'userUuid',
                'Required parameter "userUuid" was null or undefined when calling userViewUser().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/user/{user_uuid}`.replace(`{${"user_uuid"}}`, encodeURIComponent(String(requestParameters['userUuid']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ApiRoutersUserModelsUserResponseFromJSON(jsonValue));
    }

    /**
     * Fetch user details by UUID.
     * Get user detail
     */
    async userViewUser(requestParameters: UserViewUserRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ApiRoutersUserModelsUserResponse> {
        const response = await this.userViewUserRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
