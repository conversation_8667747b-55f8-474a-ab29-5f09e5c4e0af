/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface FinancialAccountTransaction
 */
export interface FinancialAccountTransaction {
    /**
     * 
     * @type {Date}
     * @memberof FinancialAccountTransaction
     */
    date: Date;
    /**
     * 
     * @type {string}
     * @memberof FinancialAccountTransaction
     */
    accountNumber: string;
    /**
     * 
     * @type {string}
     * @memberof FinancialAccountTransaction
     */
    accountName: string;
    /**
     * 
     * @type {string}
     * @memberof FinancialAccountTransaction
     */
    accountTitle: string;
    /**
     * 
     * @type {string}
     * @memberof FinancialAccountTransaction
     */
    accountType: string;
    /**
     * 
     * @type {string}
     * @memberof FinancialAccountTransaction
     */
    accountTypeDescription: string;
    /**
     * 
     * @type {string}
     * @memberof FinancialAccountTransaction
     */
    accountSegment: string;
    /**
     * 
     * @type {string}
     * @memberof FinancialAccountTransaction
     */
    accountQualifiedStatus: string;
    /**
     * 
     * @type {string}
     * @memberof FinancialAccountTransaction
     */
    accountTaxableStatus: string;
    /**
     * 
     * @type {string}
     * @memberof FinancialAccountTransaction
     */
    accountModelName: string;
    /**
     * 
     * @type {string}
     * @memberof FinancialAccountTransaction
     */
    accountLegalName: string;
    /**
     * 
     * @type {string}
     * @memberof FinancialAccountTransaction
     */
    fundId: string;
    /**
     * 
     * @type {string}
     * @memberof FinancialAccountTransaction
     */
    fundName: string;
    /**
     * 
     * @type {string}
     * @memberof FinancialAccountTransaction
     */
    fundTicker: string;
    /**
     * 
     * @type {string}
     * @memberof FinancialAccountTransaction
     */
    fundSecurityType: string;
    /**
     * 
     * @type {string}
     * @memberof FinancialAccountTransaction
     */
    fundManagementName: string;
    /**
     * 
     * @type {number}
     * @memberof FinancialAccountTransaction
     */
    fundMorningStarRating: number;
    /**
     * 
     * @type {string}
     * @memberof FinancialAccountTransaction
     */
    actionName: string;
    /**
     * 
     * @type {number}
     * @memberof FinancialAccountTransaction
     */
    quantity: number;
    /**
     * 
     * @type {Date}
     * @memberof FinancialAccountTransaction
     */
    quantityDate: Date;
    /**
     * 
     * @type {number}
     * @memberof FinancialAccountTransaction
     */
    price: number;
    /**
     * 
     * @type {Date}
     * @memberof FinancialAccountTransaction
     */
    priceDate: Date;
    /**
     * 
     * @type {number}
     * @memberof FinancialAccountTransaction
     */
    value: number;
}

/**
 * Check if a given object implements the FinancialAccountTransaction interface.
 */
export function instanceOfFinancialAccountTransaction(value: object): value is FinancialAccountTransaction {
    if (!('date' in value) || value['date'] === undefined) return false;
    if (!('accountNumber' in value) || value['accountNumber'] === undefined) return false;
    if (!('accountName' in value) || value['accountName'] === undefined) return false;
    if (!('accountTitle' in value) || value['accountTitle'] === undefined) return false;
    if (!('accountType' in value) || value['accountType'] === undefined) return false;
    if (!('accountTypeDescription' in value) || value['accountTypeDescription'] === undefined) return false;
    if (!('accountSegment' in value) || value['accountSegment'] === undefined) return false;
    if (!('accountQualifiedStatus' in value) || value['accountQualifiedStatus'] === undefined) return false;
    if (!('accountTaxableStatus' in value) || value['accountTaxableStatus'] === undefined) return false;
    if (!('accountModelName' in value) || value['accountModelName'] === undefined) return false;
    if (!('accountLegalName' in value) || value['accountLegalName'] === undefined) return false;
    if (!('fundId' in value) || value['fundId'] === undefined) return false;
    if (!('fundName' in value) || value['fundName'] === undefined) return false;
    if (!('fundTicker' in value) || value['fundTicker'] === undefined) return false;
    if (!('fundSecurityType' in value) || value['fundSecurityType'] === undefined) return false;
    if (!('fundManagementName' in value) || value['fundManagementName'] === undefined) return false;
    if (!('fundMorningStarRating' in value) || value['fundMorningStarRating'] === undefined) return false;
    if (!('actionName' in value) || value['actionName'] === undefined) return false;
    if (!('quantity' in value) || value['quantity'] === undefined) return false;
    if (!('quantityDate' in value) || value['quantityDate'] === undefined) return false;
    if (!('price' in value) || value['price'] === undefined) return false;
    if (!('priceDate' in value) || value['priceDate'] === undefined) return false;
    if (!('value' in value) || value['value'] === undefined) return false;
    return true;
}

export function FinancialAccountTransactionFromJSON(json: any): FinancialAccountTransaction {
    return FinancialAccountTransactionFromJSONTyped(json, false);
}

export function FinancialAccountTransactionFromJSONTyped(json: any, ignoreDiscriminator: boolean): FinancialAccountTransaction {
    if (json == null) {
        return json;
    }
    return {
        
        'date': (new Date(json['date'])),
        'accountNumber': json['account_number'],
        'accountName': json['account_name'],
        'accountTitle': json['account_title'],
        'accountType': json['account_type'],
        'accountTypeDescription': json['account_type_description'],
        'accountSegment': json['account_segment'],
        'accountQualifiedStatus': json['account_qualified_status'],
        'accountTaxableStatus': json['account_taxable_status'],
        'accountModelName': json['account_model_name'],
        'accountLegalName': json['account_legal_name'],
        'fundId': json['fund_id'],
        'fundName': json['fund_name'],
        'fundTicker': json['fund_ticker'],
        'fundSecurityType': json['fund_security_type'],
        'fundManagementName': json['fund_management_name'],
        'fundMorningStarRating': json['fund_morning_star_rating'],
        'actionName': json['action_name'],
        'quantity': json['quantity'],
        'quantityDate': (new Date(json['quantity_date'])),
        'price': json['price'],
        'priceDate': (new Date(json['price_date'])),
        'value': json['value'],
    };
}

export function FinancialAccountTransactionToJSON(value?: FinancialAccountTransaction | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'date': ((value['date']).toISOString().substring(0,10)),
        'account_number': value['accountNumber'],
        'account_name': value['accountName'],
        'account_title': value['accountTitle'],
        'account_type': value['accountType'],
        'account_type_description': value['accountTypeDescription'],
        'account_segment': value['accountSegment'],
        'account_qualified_status': value['accountQualifiedStatus'],
        'account_taxable_status': value['accountTaxableStatus'],
        'account_model_name': value['accountModelName'],
        'account_legal_name': value['accountLegalName'],
        'fund_id': value['fundId'],
        'fund_name': value['fundName'],
        'fund_ticker': value['fundTicker'],
        'fund_security_type': value['fundSecurityType'],
        'fund_management_name': value['fundManagementName'],
        'fund_morning_star_rating': value['fundMorningStarRating'],
        'action_name': value['actionName'],
        'quantity': value['quantity'],
        'quantity_date': ((value['quantityDate']).toISOString().substring(0,10)),
        'price': value['price'],
        'price_date': ((value['priceDate']).toISOString().substring(0,10)),
        'value': value['value'],
    };
}

