/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { Category } from './Category';
import {
    CategoryFromJSON,
    CategoryFromJSONTyped,
    CategoryToJSON,
} from './Category';
import type { NoteAudioSource } from './NoteAudioSource';
import {
    NoteAudioSourceFromJSON,
    NoteAudioSourceFromJSONTyped,
    NoteAudioSourceToJSON,
} from './NoteAudioSource';
import type { ApiRoutersNoteModelsClient } from './ApiRoutersNoteModelsClient';
import {
    ApiRoutersNoteModelsClientFromJSON,
    ApiRoutersNoteModelsClientFromJSONTyped,
    ApiRoutersNoteModelsClientToJSON,
} from './ApiRoutersNoteModelsClient';
import type { AttendeeInfoLite } from './AttendeeInfoLite';
import {
    AttendeeInfoLiteFromJSON,
    AttendeeInfoLiteFromJSONTyped,
    AttendeeInfoLiteToJSON,
} from './AttendeeInfoLite';
import type { ProcessingStatus } from './ProcessingStatus';
import {
    ProcessingStatusFromJSON,
    ProcessingStatusFromJSONTyped,
    ProcessingStatusToJSON,
} from './ProcessingStatus';

/**
 * 
 * @export
 * @interface ListNotesResponse
 */
export interface ListNotesResponse {
    /**
     * 
     * @type {string}
     * @memberof ListNotesResponse
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof ListNotesResponse
     */
    created: string;
    /**
     * 
     * @type {string}
     * @memberof ListNotesResponse
     */
    modified: string;
    /**
     * 
     * @type {string}
     * @memberof ListNotesResponse
     */
    noteType: string;
    /**
     * 
     * @type {ProcessingStatus}
     * @memberof ListNotesResponse
     */
    status: ProcessingStatus;
    /**
     * 
     * @type {boolean}
     * @memberof ListNotesResponse
     */
    isPrivate: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof ListNotesResponse
     */
    canUpdatePrivacyStatus: boolean;
    /**
     * 
     * @type {string}
     * @memberof ListNotesResponse
     */
    meetingName: string;
    /**
     * 
     * @type {ApiRoutersNoteModelsClient}
     * @memberof ListNotesResponse
     */
    client: ApiRoutersNoteModelsClient | null;
    /**
     * 
     * @type {string}
     * @memberof ListNotesResponse
     */
    scheduledStartTime: string | null;
    /**
     * 
     * @type {string}
     * @memberof ListNotesResponse
     */
    scheduledEndTime: string | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof ListNotesResponse
     */
    tags: Array<string>;
    /**
     * 
     * @type {string}
     * @memberof ListNotesResponse
     */
    meetingSourceId: string | null;
    /**
     * 
     * @type {string}
     * @memberof ListNotesResponse
     */
    scheduledEventUuid: string | null;
    /**
     * 
     * @type {Array<AttendeeInfoLite>}
     * @memberof ListNotesResponse
     */
    attendees: Array<AttendeeInfoLite>;
    /**
     * 
     * @type {Category}
     * @memberof ListNotesResponse
     */
    meetingCategory: Category | null;
    /**
     * 
     * @type {string}
     * @memberof ListNotesResponse
     */
    meetingType: string | null;
    /**
     * 
     * @type {AttendeeInfoLite}
     * @memberof ListNotesResponse
     */
    owner: AttendeeInfoLite;
    /**
     * 
     * @type {NoteAudioSource}
     * @memberof ListNotesResponse
     */
    audioSource: NoteAudioSource;
    /**
     * 
     * @type {number}
     * @memberof ListNotesResponse
     */
    meetingDurationSeconds?: number | null;
    /**
     * 
     * @type {boolean}
     * @memberof ListNotesResponse
     */
    autojoinAvailable?: boolean | null;
    /**
     * 
     * @type {boolean}
     * @memberof ListNotesResponse
     */
    autojoinEnabled?: boolean | null;
    /**
     * 
     * @type {boolean}
     * @memberof ListNotesResponse
     */
    autojoinEditable?: boolean | null;
    /**
     * 
     * @type {string}
     * @memberof ListNotesResponse
     */
    meetingLink?: string | null;
}



/**
 * Check if a given object implements the ListNotesResponse interface.
 */
export function instanceOfListNotesResponse(value: object): value is ListNotesResponse {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('created' in value) || value['created'] === undefined) return false;
    if (!('modified' in value) || value['modified'] === undefined) return false;
    if (!('noteType' in value) || value['noteType'] === undefined) return false;
    if (!('status' in value) || value['status'] === undefined) return false;
    if (!('isPrivate' in value) || value['isPrivate'] === undefined) return false;
    if (!('canUpdatePrivacyStatus' in value) || value['canUpdatePrivacyStatus'] === undefined) return false;
    if (!('meetingName' in value) || value['meetingName'] === undefined) return false;
    if (!('client' in value) || value['client'] === undefined) return false;
    if (!('scheduledStartTime' in value) || value['scheduledStartTime'] === undefined) return false;
    if (!('scheduledEndTime' in value) || value['scheduledEndTime'] === undefined) return false;
    if (!('tags' in value) || value['tags'] === undefined) return false;
    if (!('meetingSourceId' in value) || value['meetingSourceId'] === undefined) return false;
    if (!('scheduledEventUuid' in value) || value['scheduledEventUuid'] === undefined) return false;
    if (!('attendees' in value) || value['attendees'] === undefined) return false;
    if (!('meetingCategory' in value) || value['meetingCategory'] === undefined) return false;
    if (!('meetingType' in value) || value['meetingType'] === undefined) return false;
    if (!('owner' in value) || value['owner'] === undefined) return false;
    if (!('audioSource' in value) || value['audioSource'] === undefined) return false;
    return true;
}

export function ListNotesResponseFromJSON(json: any): ListNotesResponse {
    return ListNotesResponseFromJSONTyped(json, false);
}

export function ListNotesResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): ListNotesResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'created': json['created'],
        'modified': json['modified'],
        'noteType': json['note_type'],
        'status': ProcessingStatusFromJSON(json['status']),
        'isPrivate': json['is_private'],
        'canUpdatePrivacyStatus': json['can_update_privacy_status'],
        'meetingName': json['meeting_name'],
        'client': ApiRoutersNoteModelsClientFromJSON(json['client']),
        'scheduledStartTime': json['scheduled_start_time'],
        'scheduledEndTime': json['scheduled_end_time'],
        'tags': json['tags'],
        'meetingSourceId': json['meeting_source_id'],
        'scheduledEventUuid': json['scheduled_event_uuid'],
        'attendees': ((json['attendees'] as Array<any>).map(AttendeeInfoLiteFromJSON)),
        'meetingCategory': CategoryFromJSON(json['meeting_category']),
        'meetingType': json['meeting_type'],
        'owner': AttendeeInfoLiteFromJSON(json['owner']),
        'audioSource': NoteAudioSourceFromJSON(json['audio_source']),
        'meetingDurationSeconds': json['meeting_duration_seconds'] == null ? undefined : json['meeting_duration_seconds'],
        'autojoinAvailable': json['autojoin_available'] == null ? undefined : json['autojoin_available'],
        'autojoinEnabled': json['autojoin_enabled'] == null ? undefined : json['autojoin_enabled'],
        'autojoinEditable': json['autojoin_editable'] == null ? undefined : json['autojoin_editable'],
        'meetingLink': json['meeting_link'] == null ? undefined : json['meeting_link'],
    };
}

export function ListNotesResponseToJSON(value?: ListNotesResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'created': value['created'],
        'modified': value['modified'],
        'note_type': value['noteType'],
        'status': ProcessingStatusToJSON(value['status']),
        'is_private': value['isPrivate'],
        'can_update_privacy_status': value['canUpdatePrivacyStatus'],
        'meeting_name': value['meetingName'],
        'client': ApiRoutersNoteModelsClientToJSON(value['client']),
        'scheduled_start_time': value['scheduledStartTime'],
        'scheduled_end_time': value['scheduledEndTime'],
        'tags': value['tags'],
        'meeting_source_id': value['meetingSourceId'],
        'scheduled_event_uuid': value['scheduledEventUuid'],
        'attendees': ((value['attendees'] as Array<any>).map(AttendeeInfoLiteToJSON)),
        'meeting_category': CategoryToJSON(value['meetingCategory']),
        'meeting_type': value['meetingType'],
        'owner': AttendeeInfoLiteToJSON(value['owner']),
        'audio_source': NoteAudioSourceToJSON(value['audioSource']),
        'meeting_duration_seconds': value['meetingDurationSeconds'],
        'autojoin_available': value['autojoinAvailable'],
        'autojoin_enabled': value['autojoinEnabled'],
        'autojoin_editable': value['autojoinEditable'],
        'meeting_link': value['meetingLink'],
    };
}

