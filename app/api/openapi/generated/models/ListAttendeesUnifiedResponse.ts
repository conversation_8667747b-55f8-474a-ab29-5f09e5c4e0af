/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { UnifiedAttendeeOptionInfo } from './UnifiedAttendeeOptionInfo';
import {
    UnifiedAttendeeOptionInfoFromJSON,
    UnifiedAttendeeOptionInfoFromJSONTyped,
    UnifiedAttendeeOptionInfoToJSON,
} from './UnifiedAttendeeOptionInfo';

/**
 * 
 * @export
 * @interface ListAttendeesUnifiedResponse
 */
export interface ListAttendeesUnifiedResponse {
    /**
     * 
     * @type {Array<UnifiedAttendeeOptionInfo>}
     * @memberof ListAttendeesUnifiedResponse
     */
    attendees: Array<UnifiedAttendeeOptionInfo>;
    /**
     * 
     * @type {string}
     * @memberof ListAttendeesUnifiedResponse
     */
    nextPageToken: string;
}

/**
 * Check if a given object implements the ListAttendeesUnifiedResponse interface.
 */
export function instanceOfListAttendeesUnifiedResponse(value: object): value is ListAttendeesUnifiedResponse {
    if (!('attendees' in value) || value['attendees'] === undefined) return false;
    if (!('nextPageToken' in value) || value['nextPageToken'] === undefined) return false;
    return true;
}

export function ListAttendeesUnifiedResponseFromJSON(json: any): ListAttendeesUnifiedResponse {
    return ListAttendeesUnifiedResponseFromJSONTyped(json, false);
}

export function ListAttendeesUnifiedResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): ListAttendeesUnifiedResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'attendees': ((json['attendees'] as Array<any>).map(UnifiedAttendeeOptionInfoFromJSON)),
        'nextPageToken': json['next_page_token'],
    };
}

export function ListAttendeesUnifiedResponseToJSON(value?: ListAttendeesUnifiedResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'attendees': ((value['attendees'] as Array<any>).map(UnifiedAttendeeOptionInfoToJSON)),
        'next_page_token': value['nextPageToken'],
    };
}

