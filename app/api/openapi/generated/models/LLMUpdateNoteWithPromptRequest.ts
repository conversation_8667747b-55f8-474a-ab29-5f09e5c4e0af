/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by Z<PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface LLMUpdateNoteWithPromptRequest
 */
export interface LLMUpdateNoteWithPromptRequest {
    /**
     * User's natural language request for how they want the note updated
     * @type {string}
     * @memberof LLMUpdateNoteWithPromptRequest
     */
    userPrompt: string;
}

/**
 * Check if a given object implements the LLMUpdateNoteWithPromptRequest interface.
 */
export function instanceOfLLMUpdateNoteWithPromptRequest(value: object): value is LLMUpdateNoteWithPromptRequest {
    if (!('userPrompt' in value) || value['userPrompt'] === undefined) return false;
    return true;
}

export function LLMUpdateNoteWithPromptRequestFromJSON(json: any): LLMUpdateNoteWithPromptRequest {
    return LLMUpdateNoteWithPromptRequestFromJSONTyped(json, false);
}

export function LLMUpdateNoteWithPromptRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): LLMUpdateNoteWithPromptRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'userPrompt': json['user_prompt'],
    };
}

export function LLMUpdateNoteWithPromptRequestToJSON(value?: LLMUpdateNoteWithPromptRequest | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'user_prompt': value['userPrompt'],
    };
}

