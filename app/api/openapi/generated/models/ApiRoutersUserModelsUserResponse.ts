/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { EntitlementType } from './EntitlementType';
import {
    EntitlementTypeFromJSON,
    EntitlementTypeFromJSONTyped,
    EntitlementTypeToJSON,
} from './EntitlementType';
import type { LicenseType } from './LicenseType';
import {
    LicenseTypeFromJSON,
    LicenseTypeFromJSONTyped,
    LicenseTypeToJSON,
} from './LicenseType';

/**
 * 
 * @export
 * @interface ApiRoutersUserModelsUserResponse
 */
export interface ApiRoutersUserModelsUserResponse {
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersUserModelsUserResponse
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersUserModelsUserResponse
     */
    email?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersUserModelsUserResponse
     */
    username: string;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersUserModelsUserResponse
     */
    name: string;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersUserModelsUserResponse
     */
    firstName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersUserModelsUserResponse
     */
    middleName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersUserModelsUserResponse
     */
    lastName?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof ApiRoutersUserModelsUserResponse
     */
    isActive: boolean;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersUserModelsUserResponse
     */
    status: string;
    /**
     * 
     * @type {Date}
     * @memberof ApiRoutersUserModelsUserResponse
     */
    created: Date;
    /**
     * 
     * @type {Date}
     * @memberof ApiRoutersUserModelsUserResponse
     */
    modified: Date;
    /**
     * 
     * @type {LicenseType}
     * @memberof ApiRoutersUserModelsUserResponse
     */
    licenseType?: LicenseType | null;
    /**
     * 
     * @type {Array<EntitlementType>}
     * @memberof ApiRoutersUserModelsUserResponse
     */
    entitlements?: Array<EntitlementType>;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersUserModelsUserResponse
     */
    phoneNumber?: string;
}



/**
 * Check if a given object implements the ApiRoutersUserModelsUserResponse interface.
 */
export function instanceOfApiRoutersUserModelsUserResponse(value: object): value is ApiRoutersUserModelsUserResponse {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('username' in value) || value['username'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    if (!('isActive' in value) || value['isActive'] === undefined) return false;
    if (!('status' in value) || value['status'] === undefined) return false;
    if (!('created' in value) || value['created'] === undefined) return false;
    if (!('modified' in value) || value['modified'] === undefined) return false;
    return true;
}

export function ApiRoutersUserModelsUserResponseFromJSON(json: any): ApiRoutersUserModelsUserResponse {
    return ApiRoutersUserModelsUserResponseFromJSONTyped(json, false);
}

export function ApiRoutersUserModelsUserResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): ApiRoutersUserModelsUserResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'email': json['email'] == null ? undefined : json['email'],
        'username': json['username'],
        'name': json['name'],
        'firstName': json['first_name'] == null ? undefined : json['first_name'],
        'middleName': json['middle_name'] == null ? undefined : json['middle_name'],
        'lastName': json['last_name'] == null ? undefined : json['last_name'],
        'isActive': json['is_active'],
        'status': json['status'],
        'created': (new Date(json['created'])),
        'modified': (new Date(json['modified'])),
        'licenseType': json['license_type'] == null ? undefined : LicenseTypeFromJSON(json['license_type']),
        'entitlements': json['entitlements'] == null ? undefined : ((json['entitlements'] as Array<any>).map(EntitlementTypeFromJSON)),
        'phoneNumber': json['phone_number'] == null ? undefined : json['phone_number'],
    };
}

export function ApiRoutersUserModelsUserResponseToJSON(value?: ApiRoutersUserModelsUserResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'email': value['email'],
        'username': value['username'],
        'name': value['name'],
        'first_name': value['firstName'],
        'middle_name': value['middleName'],
        'last_name': value['lastName'],
        'is_active': value['isActive'],
        'status': value['status'],
        'created': ((value['created']).toISOString()),
        'modified': ((value['modified']).toISOString()),
        'license_type': LicenseTypeToJSON(value['licenseType']),
        'entitlements': value['entitlements'] == null ? undefined : ((value['entitlements'] as Array<any>).map(EntitlementTypeToJSON)),
        'phone_number': value['phoneNumber'],
    };
}

