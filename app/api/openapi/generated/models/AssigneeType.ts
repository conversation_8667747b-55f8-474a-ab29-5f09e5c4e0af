/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * Enumeration for different types of tasks.
 * @export
 */
export const AssigneeType = {
    AdvisorTask: 'advisor_task',
    ClientFollowup: 'client_followup'
} as const;
export type AssigneeType = typeof AssigneeType[keyof typeof AssigneeType];


export function instanceOfAssigneeType(value: any): boolean {
    for (const key in AssigneeType) {
        if (Object.prototype.hasOwnProperty.call(AssigneeType, key)) {
            if (AssigneeType[key as keyof typeof AssigneeType] === value) {
                return true;
            }
        }
    }
    return false;
}

export function AssigneeTypeFromJSON(json: any): AssigneeType {
    return AssigneeTypeFromJSONTyped(json, false);
}

export function AssigneeTypeFromJSONTyped(json: any, ignoreDiscriminator: boolean): AssigneeType {
    return json as AssigneeType;
}

export function AssigneeTypeToJSON(value?: AssigneeType | null): any {
    return value as any;
}

