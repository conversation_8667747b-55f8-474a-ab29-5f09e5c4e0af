/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface CreateUserResponse
 */
export interface CreateUserResponse {
    /**
     * 
     * @type {string}
     * @memberof CreateUserResponse
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof CreateUserResponse
     */
    status: CreateUserResponseStatusEnum;
}


/**
 * @export
 */
export const CreateUserResponseStatusEnum = {
    Active: 'active',
    Waitlisted: 'waitlisted'
} as const;
export type CreateUserResponseStatusEnum = typeof CreateUserResponseStatusEnum[keyof typeof CreateUserResponseStatusEnum];


/**
 * Check if a given object implements the CreateUserResponse interface.
 */
export function instanceOfCreateUserResponse(value: object): value is CreateUserResponse {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('status' in value) || value['status'] === undefined) return false;
    return true;
}

export function CreateUserResponseFromJSON(json: any): CreateUserResponse {
    return CreateUserResponseFromJSONTyped(json, false);
}

export function CreateUserResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): CreateUserResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'status': json['status'],
    };
}

export function CreateUserResponseToJSON(value?: CreateUserResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'status': value['status'],
    };
}

