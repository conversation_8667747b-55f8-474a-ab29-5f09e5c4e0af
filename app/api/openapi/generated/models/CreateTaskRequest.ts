/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { AssigneeType } from './AssigneeType';
import {
    AssigneeTypeFromJSON,
    AssigneeTypeFromJSONTyped,
    AssigneeTypeToJSON,
} from './AssigneeType';
import type { CRMUser } from './CRMUser';
import {
    CRMUserFromJSON,
    CRMUserFromJSONTyped,
    CRMUserToJSON,
} from './CRMUser';

/**
 * 
 * @export
 * @interface CreateTaskRequest
 */
export interface CreateTaskRequest {
    /**
     * 
     * @type {string}
     * @memberof CreateTaskRequest
     */
    title: string;
    /**
     * 
     * @type {string}
     * @memberof CreateTaskRequest
     */
    ownerUuid: string;
    /**
     * 
     * @type {string}
     * @memberof CreateTaskRequest
     */
    description?: string | null;
    /**
     * 
     * @type {Date}
     * @memberof CreateTaskRequest
     */
    dueDate?: Date | null;
    /**
     * 
     * @type {string}
     * @memberof CreateTaskRequest
     */
    parentNoteUuid?: string | null;
    /**
     * 
     * @type {CRMUser}
     * @memberof CreateTaskRequest
     */
    crmAssignee?: CRMUser | null;
    /**
     * 
     * @type {AssigneeType}
     * @memberof CreateTaskRequest
     */
    assigneeType?: AssigneeType | null;
}



/**
 * Check if a given object implements the CreateTaskRequest interface.
 */
export function instanceOfCreateTaskRequest(value: object): value is CreateTaskRequest {
    if (!('title' in value) || value['title'] === undefined) return false;
    if (!('ownerUuid' in value) || value['ownerUuid'] === undefined) return false;
    return true;
}

export function CreateTaskRequestFromJSON(json: any): CreateTaskRequest {
    return CreateTaskRequestFromJSONTyped(json, false);
}

export function CreateTaskRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): CreateTaskRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'title': json['title'],
        'ownerUuid': json['owner_uuid'],
        'description': json['description'] == null ? undefined : json['description'],
        'dueDate': json['due_date'] == null ? undefined : (new Date(json['due_date'])),
        'parentNoteUuid': json['parent_note_uuid'] == null ? undefined : json['parent_note_uuid'],
        'crmAssignee': json['crm_assignee'] == null ? undefined : CRMUserFromJSON(json['crm_assignee']),
        'assigneeType': json['assignee_type'] == null ? undefined : AssigneeTypeFromJSON(json['assignee_type']),
    };
}

export function CreateTaskRequestToJSON(value?: CreateTaskRequest | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'title': value['title'],
        'owner_uuid': value['ownerUuid'],
        'description': value['description'],
        'due_date': value['dueDate'] == null ? undefined : ((value['dueDate'] as any).toISOString()),
        'parent_note_uuid': value['parentNoteUuid'],
        'crm_assignee': CRMUserToJSON(value['crmAssignee']),
        'assignee_type': AssigneeTypeToJSON(value['assigneeType']),
    };
}

