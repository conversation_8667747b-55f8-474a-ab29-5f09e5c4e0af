/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const PrivacyStatus = {
    NoTeamAccess: 'no_team_access',
    Default: 'default'
} as const;
export type PrivacyStatus = typeof PrivacyStatus[keyof typeof PrivacyStatus];


export function instanceOfPrivacyStatus(value: any): boolean {
    for (const key in PrivacyStatus) {
        if (Object.prototype.hasOwnProperty.call(PrivacyStatus, key)) {
            if (PrivacyStatus[key as keyof typeof PrivacyStatus] === value) {
                return true;
            }
        }
    }
    return false;
}

export function PrivacyStatusFromJSON(json: any): PrivacyStatus {
    return PrivacyStatusFromJSONTyped(json, false);
}

export function PrivacyStatusFromJSONTyped(json: any, ignoreDiscriminator: boolean): PrivacyStatus {
    return json as PrivacyStatus;
}

export function PrivacyStatusToJSON(value?: PrivacyStatus | null): any {
    return value as any;
}

