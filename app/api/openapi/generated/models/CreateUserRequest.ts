/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { EntitlementType } from './EntitlementType';
import {
    EntitlementTypeFromJSON,
    EntitlementTypeFromJSONTyped,
    EntitlementTypeToJSON,
} from './EntitlementType';

/**
 * 
 * @export
 * @interface CreateUserRequest
 */
export interface CreateUserRequest {
    /**
     * 
     * @type {string}
     * @memberof CreateUserRequest
     */
    email: string;
    /**
     * 
     * @type {string}
     * @memberof CreateUserRequest
     */
    name: string;
    /**
     * 
     * @type {string}
     * @memberof CreateUserRequest
     */
    firstName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CreateUserRequest
     */
    middleName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CreateUserRequest
     */
    lastName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof CreateUserRequest
     */
    phoneNumber?: string;
    /**
     * 
     * @type {string}
     * @memberof CreateUserRequest
     */
    phonePin?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof CreateUserRequest
     */
    sendWelcomeEmail?: boolean;
    /**
     * 
     * @type {Array<EntitlementType>}
     * @memberof CreateUserRequest
     */
    entitlements?: Array<EntitlementType>;
}

/**
 * Check if a given object implements the CreateUserRequest interface.
 */
export function instanceOfCreateUserRequest(value: object): value is CreateUserRequest {
    if (!('email' in value) || value['email'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function CreateUserRequestFromJSON(json: any): CreateUserRequest {
    return CreateUserRequestFromJSONTyped(json, false);
}

export function CreateUserRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): CreateUserRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'email': json['email'],
        'name': json['name'],
        'firstName': json['first_name'] == null ? undefined : json['first_name'],
        'middleName': json['middle_name'] == null ? undefined : json['middle_name'],
        'lastName': json['last_name'] == null ? undefined : json['last_name'],
        'phoneNumber': json['phone_number'] == null ? undefined : json['phone_number'],
        'phonePin': json['phone_pin'] == null ? undefined : json['phone_pin'],
        'sendWelcomeEmail': json['send_welcome_email'] == null ? undefined : json['send_welcome_email'],
        'entitlements': json['entitlements'] == null ? undefined : ((json['entitlements'] as Array<any>).map(EntitlementTypeFromJSON)),
    };
}

export function CreateUserRequestToJSON(value?: CreateUserRequest | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'email': value['email'],
        'name': value['name'],
        'first_name': value['firstName'],
        'middle_name': value['middleName'],
        'last_name': value['lastName'],
        'phone_number': value['phoneNumber'],
        'phone_pin': value['phonePin'],
        'send_welcome_email': value['sendWelcomeEmail'],
        'entitlements': value['entitlements'] == null ? undefined : ((value['entitlements'] as Array<any>).map(EntitlementTypeToJSON)),
    };
}

