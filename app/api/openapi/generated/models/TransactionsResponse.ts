/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { FinancialAccountTransaction } from './FinancialAccountTransaction';
import {
    FinancialAccountTransactionFromJSON,
    FinancialAccountTransactionFromJSONTyped,
    FinancialAccountTransactionToJSON,
} from './FinancialAccountTransaction';

/**
 * Information about a client's financial transactions.
 * @export
 * @interface TransactionsResponse
 */
export interface TransactionsResponse {
    /**
     * 
     * @type {Date}
     * @memberof TransactionsResponse
     */
    startDate: Date;
    /**
     * 
     * @type {Date}
     * @memberof TransactionsResponse
     */
    endDate: Date;
    /**
     * 
     * @type {Date}
     * @memberof TransactionsResponse
     */
    asOfDate: Date;
    /**
     * 
     * @type {Array<FinancialAccountTransaction>}
     * @memberof TransactionsResponse
     */
    transactions: Array<FinancialAccountTransaction>;
}

/**
 * Check if a given object implements the TransactionsResponse interface.
 */
export function instanceOfTransactionsResponse(value: object): value is TransactionsResponse {
    if (!('startDate' in value) || value['startDate'] === undefined) return false;
    if (!('endDate' in value) || value['endDate'] === undefined) return false;
    if (!('asOfDate' in value) || value['asOfDate'] === undefined) return false;
    if (!('transactions' in value) || value['transactions'] === undefined) return false;
    return true;
}

export function TransactionsResponseFromJSON(json: any): TransactionsResponse {
    return TransactionsResponseFromJSONTyped(json, false);
}

export function TransactionsResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): TransactionsResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'startDate': (new Date(json['start_date'])),
        'endDate': (new Date(json['end_date'])),
        'asOfDate': (new Date(json['as_of_date'])),
        'transactions': ((json['transactions'] as Array<any>).map(FinancialAccountTransactionFromJSON)),
    };
}

export function TransactionsResponseToJSON(value?: TransactionsResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'start_date': ((value['startDate']).toISOString().substring(0,10)),
        'end_date': ((value['endDate']).toISOString().substring(0,10)),
        'as_of_date': ((value['asOfDate']).toISOString().substring(0,10)),
        'transactions': ((value['transactions'] as Array<any>).map(FinancialAccountTransactionToJSON)),
    };
}

