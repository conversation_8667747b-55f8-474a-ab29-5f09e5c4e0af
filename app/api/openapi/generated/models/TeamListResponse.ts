/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { TeamResponse } from './TeamResponse';
import {
    TeamResponseFromJSON,
    TeamResponseFromJSONTyped,
    TeamResponseToJSON,
} from './TeamResponse';

/**
 * Response schema for listing teams.
 * @export
 * @interface TeamListResponse
 */
export interface TeamListResponse {
    /**
     * List of teams
     * @type {Array<TeamResponse>}
     * @memberof TeamListResponse
     */
    teams?: Array<TeamResponse>;
}

/**
 * Check if a given object implements the TeamListResponse interface.
 */
export function instanceOfTeamListResponse(value: object): value is TeamListResponse {
    return true;
}

export function TeamListResponseFromJSON(json: any): TeamListResponse {
    return TeamListResponseFromJSONTyped(json, false);
}

export function TeamListResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): TeamListResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'teams': json['teams'] == null ? undefined : ((json['teams'] as Array<any>).map(TeamResponseFromJSON)),
    };
}

export function TeamListResponseToJSON(value?: TeamListResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'teams': value['teams'] == null ? undefined : ((value['teams'] as Array<any>).map(TeamResponseToJSON)),
    };
}

