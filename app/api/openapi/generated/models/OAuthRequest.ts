/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface OAuthRequest
 */
export interface OAuthRequest {
    /**
     * 
     * @type {string}
     * @memberof OAuthRequest
     */
    authorizationCode: string;
    /**
     * 
     * @type {string}
     * @memberof OAuthRequest
     */
    requestUrl: string;
    /**
     * 
     * @type {string}
     * @memberof OAuthRequest
     */
    provider: OAuthRequestProviderEnum;
    /**
     * 
     * @type {string}
     * @memberof OAuthRequest
     */
    codeVerifier?: string | null;
    /**
     * 
     * @type {string}
     * @memberof OAuthRequest
     */
    crmType?: OAuthRequestCrmTypeEnum | null;
}


/**
 * @export
 */
export const OAuthRequestProviderEnum = {
    Wealthbox: 'wealthbox',
    Microsoft: 'microsoft',
    Google: 'google',
    Gmail: 'gmail',
    OutlookEmail: 'outlook_email',
    Salesforce: 'salesforce',
    MicrosoftDynamics: 'microsoft_dynamics',
    AdvisorEngine: 'advisor_engine',
    Hubspot: 'hubspot'
} as const;
export type OAuthRequestProviderEnum = typeof OAuthRequestProviderEnum[keyof typeof OAuthRequestProviderEnum];

/**
 * @export
 */
export const OAuthRequestCrmTypeEnum = {
    Base: 'base',
    Xlr8: 'xlr8',
    SalenticaElements: 'salentica_elements',
    BlackDiamond: 'black_diamond',
    Practifi: 'practifi',
    SalenticaEngage: 'salentica_engage',
    Tamarac: 'tamarac',
    Amplify: 'amplify'
} as const;
export type OAuthRequestCrmTypeEnum = typeof OAuthRequestCrmTypeEnum[keyof typeof OAuthRequestCrmTypeEnum];


/**
 * Check if a given object implements the OAuthRequest interface.
 */
export function instanceOfOAuthRequest(value: object): value is OAuthRequest {
    if (!('authorizationCode' in value) || value['authorizationCode'] === undefined) return false;
    if (!('requestUrl' in value) || value['requestUrl'] === undefined) return false;
    if (!('provider' in value) || value['provider'] === undefined) return false;
    return true;
}

export function OAuthRequestFromJSON(json: any): OAuthRequest {
    return OAuthRequestFromJSONTyped(json, false);
}

export function OAuthRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): OAuthRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'authorizationCode': json['authorization_code'],
        'requestUrl': json['request_url'],
        'provider': json['provider'],
        'codeVerifier': json['code_verifier'] == null ? undefined : json['code_verifier'],
        'crmType': json['crm_type'] == null ? undefined : json['crm_type'],
    };
}

export function OAuthRequestToJSON(value?: OAuthRequest | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'authorization_code': value['authorizationCode'],
        'request_url': value['requestUrl'],
        'provider': value['provider'],
        'code_verifier': value['codeVerifier'],
        'crm_type': value['crmType'],
    };
}

