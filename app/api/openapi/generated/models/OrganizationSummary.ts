/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * Summary information about an organization for team responses.
 * @export
 * @interface OrganizationSummary
 */
export interface OrganizationSummary {
    /**
     * Organization UUID
     * @type {string}
     * @memberof OrganizationSummary
     */
    uuid: string;
    /**
     * Organization name
     * @type {string}
     * @memberof OrganizationSummary
     */
    name: string;
}

/**
 * Check if a given object implements the OrganizationSummary interface.
 */
export function instanceOfOrganizationSummary(value: object): value is OrganizationSummary {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function OrganizationSummaryFromJSON(json: any): OrganizationSummary {
    return OrganizationSummaryFromJSONTyped(json, false);
}

export function OrganizationSummaryFromJSONTyped(json: any, ignoreDiscriminator: boolean): OrganizationSummary {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'name': json['name'],
    };
}

export function OrganizationSummaryToJSON(value?: OrganizationSummary | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'name': value['name'],
    };
}

