/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const LicenseType = {
    Advisor: 'advisor',
    Csa: 'csa',
    Staff: 'staff'
} as const;
export type LicenseType = typeof LicenseType[keyof typeof LicenseType];


export function instanceOfLicenseType(value: any): boolean {
    for (const key in LicenseType) {
        if (Object.prototype.hasOwnProperty.call(LicenseType, key)) {
            if (LicenseType[key as keyof typeof LicenseType] === value) {
                return true;
            }
        }
    }
    return false;
}

export function LicenseTypeFromJSON(json: any): LicenseType {
    return LicenseTypeFromJSONTyped(json, false);
}

export function LicenseTypeFromJSONTyped(json: any, ignoreDiscriminator: boolean): LicenseType {
    return json as LicenseType;
}

export function LicenseTypeToJSON(value?: LicenseType | null): any {
    return value as any;
}

