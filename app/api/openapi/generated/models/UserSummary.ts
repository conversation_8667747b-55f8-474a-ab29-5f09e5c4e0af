/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * Summary information about a user for team responses.
 * @export
 * @interface UserSummary
 */
export interface UserSummary {
    /**
     * User UUID
     * @type {string}
     * @memberof UserSummary
     */
    uuid: string;
    /**
     * User's display name
     * @type {string}
     * @memberof UserSummary
     */
    name: string;
}

/**
 * Check if a given object implements the UserSummary interface.
 */
export function instanceOfUserSummary(value: object): value is UserSummary {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function UserSummaryFromJSON(json: any): UserSummary {
    return UserSummaryFromJSONTyped(json, false);
}

export function UserSummaryFromJSONTyped(json: any, ignoreDiscriminator: boolean): UserSummary {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'name': json['name'],
    };
}

export function UserSummaryToJSON(value?: UserSummary | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'name': value['name'],
    };
}

