/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { ApiRoutersAttendeeClientResponse } from './ApiRoutersAttendeeClientResponse';
import {
    ApiRoutersAttendeeClientResponseFromJSON,
    ApiRoutersAttendeeClientResponseFromJSONTyped,
    ApiRoutersAttendeeClientResponseToJSON,
} from './ApiRoutersAttendeeClientResponse';
import type { ApiRoutersAttendeeUserResponse } from './ApiRoutersAttendeeUserResponse';
import {
    ApiRoutersAttendeeUserResponseFromJSON,
    ApiRoutersAttendeeUserResponseFromJSONTyped,
    ApiRoutersAttendeeUserResponseToJSON,
} from './ApiRoutersAttendeeUserResponse';

/**
 * 
 * @export
 * @interface ListAttendeesResponse
 */
export interface ListAttendeesResponse {
    /**
     * 
     * @type {Array<ApiRoutersAttendeeUserResponse>}
     * @memberof ListAttendeesResponse
     */
    users: Array<ApiRoutersAttendeeUserResponse>;
    /**
     * 
     * @type {Array<ApiRoutersAttendeeClientResponse>}
     * @memberof ListAttendeesResponse
     */
    clients: Array<ApiRoutersAttendeeClientResponse>;
}

/**
 * Check if a given object implements the ListAttendeesResponse interface.
 */
export function instanceOfListAttendeesResponse(value: object): value is ListAttendeesResponse {
    if (!('users' in value) || value['users'] === undefined) return false;
    if (!('clients' in value) || value['clients'] === undefined) return false;
    return true;
}

export function ListAttendeesResponseFromJSON(json: any): ListAttendeesResponse {
    return ListAttendeesResponseFromJSONTyped(json, false);
}

export function ListAttendeesResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): ListAttendeesResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'users': ((json['users'] as Array<any>).map(ApiRoutersAttendeeUserResponseFromJSON)),
        'clients': ((json['clients'] as Array<any>).map(ApiRoutersAttendeeClientResponseFromJSON)),
    };
}

export function ListAttendeesResponseToJSON(value?: ListAttendeesResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'users': ((value['users'] as Array<any>).map(ApiRoutersAttendeeUserResponseToJSON)),
        'clients': ((value['clients'] as Array<any>).map(ApiRoutersAttendeeClientResponseToJSON)),
    };
}

