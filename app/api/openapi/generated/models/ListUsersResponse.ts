/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { ApiRoutersUserModelsUserResponse } from './ApiRoutersUserModelsUserResponse';
import {
    ApiRoutersUserModelsUserResponseFromJSON,
    ApiRoutersUserModelsUserResponseFromJSONTyped,
    ApiRoutersUserModelsUserResponseToJSON,
} from './ApiRoutersUserModelsUserResponse';

/**
 * 
 * @export
 * @interface ListUsersResponse
 */
export interface ListUsersResponse {
    /**
     * 
     * @type {Array<ApiRoutersUserModelsUserResponse>}
     * @memberof ListUsersResponse
     */
    users: Array<ApiRoutersUserModelsUserResponse>;
    /**
     * 
     * @type {string}
     * @memberof ListUsersResponse
     */
    nextPageToken?: string | null;
}

/**
 * Check if a given object implements the ListUsersResponse interface.
 */
export function instanceOfListUsersResponse(value: object): value is ListUsersResponse {
    if (!('users' in value) || value['users'] === undefined) return false;
    return true;
}

export function ListUsersResponseFromJSON(json: any): ListUsersResponse {
    return ListUsersResponseFromJSONTyped(json, false);
}

export function ListUsersResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): ListUsersResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'users': ((json['users'] as Array<any>).map(ApiRoutersUserModelsUserResponseFromJSON)),
        'nextPageToken': json['next_page_token'] == null ? undefined : json['next_page_token'],
    };
}

export function ListUsersResponseToJSON(value?: ListUsersResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'users': ((value['users'] as Array<any>).map(ApiRoutersUserModelsUserResponseToJSON)),
        'next_page_token': value['nextPageToken'],
    };
}

