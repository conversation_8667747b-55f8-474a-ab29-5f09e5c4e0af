/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { OrganizationSummary } from './OrganizationSummary';
import {
    OrganizationSummaryFromJSON,
    OrganizationSummaryFromJSONTyped,
    OrganizationSummaryToJSON,
} from './OrganizationSummary';

/**
 * Response schema for team information.
 * @export
 * @interface TeamResponse
 */
export interface TeamResponse {
    /**
     * Team UUID
     * @type {string}
     * @memberof TeamResponse
     */
    uuid: string;
    /**
     * Team name
     * @type {string}
     * @memberof TeamResponse
     */
    name: string;
    /**
     * Organization information
     * @type {OrganizationSummary}
     * @memberof TeamResponse
     */
    organization: OrganizationSummary;
    /**
     * When the team was created
     * @type {Date}
     * @memberof TeamResponse
     */
    created: Date;
    /**
     * When the team was last modified
     * @type {Date}
     * @memberof TeamResponse
     */
    modified: Date;
}

/**
 * Check if a given object implements the TeamResponse interface.
 */
export function instanceOfTeamResponse(value: object): value is TeamResponse {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    if (!('organization' in value) || value['organization'] === undefined) return false;
    if (!('created' in value) || value['created'] === undefined) return false;
    if (!('modified' in value) || value['modified'] === undefined) return false;
    return true;
}

export function TeamResponseFromJSON(json: any): TeamResponse {
    return TeamResponseFromJSONTyped(json, false);
}

export function TeamResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): TeamResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'name': json['name'],
        'organization': OrganizationSummaryFromJSON(json['organization']),
        'created': (new Date(json['created'])),
        'modified': (new Date(json['modified'])),
    };
}

export function TeamResponseToJSON(value?: TeamResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'name': value['name'],
        'organization': OrganizationSummaryToJSON(value['organization']),
        'created': ((value['created']).toISOString()),
        'modified': ((value['modified']).toISOString()),
    };
}

