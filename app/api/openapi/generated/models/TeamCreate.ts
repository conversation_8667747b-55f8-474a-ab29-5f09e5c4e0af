/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * Schema for creating a new team.
 * @export
 * @interface TeamCreate
 */
export interface TeamCreate {
    /**
     * UUID of the organization this team belongs to
     * @type {string}
     * @memberof TeamCreate
     */
    organizationId: string;
    /**
     * Name of the team
     * @type {string}
     * @memberof TeamCreate
     */
    name: string;
    /**
     * 
     * @type {Array<string>}
     * @memberof TeamCreate
     */
    memberIds?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof TeamCreate
     */
    viewerIds?: Array<string> | null;
}

/**
 * Check if a given object implements the TeamCreate interface.
 */
export function instanceOfTeamCreate(value: object): value is TeamCreate {
    if (!('organizationId' in value) || value['organizationId'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function TeamCreateFromJSON(json: any): TeamCreate {
    return TeamCreateFromJSONTyped(json, false);
}

export function TeamCreateFromJSONTyped(json: any, ignoreDiscriminator: boolean): TeamCreate {
    if (json == null) {
        return json;
    }
    return {
        
        'organizationId': json['organization_id'],
        'name': json['name'],
        'memberIds': json['member_ids'] == null ? undefined : json['member_ids'],
        'viewerIds': json['viewer_ids'] == null ? undefined : json['viewer_ids'],
    };
}

export function TeamCreateToJSON(value?: TeamCreate | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'organization_id': value['organizationId'],
        'name': value['name'],
        'member_ids': value['memberIds'],
        'viewer_ids': value['viewerIds'],
    };
}

