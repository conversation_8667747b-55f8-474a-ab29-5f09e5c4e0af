/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { EntitlementType } from './EntitlementType';
import {
    EntitlementTypeFromJSON,
    EntitlementTypeFromJSONTyped,
    EntitlementTypeToJSON,
} from './EntitlementType';

/**
 * 
 * @export
 * @interface UserUpdate
 */
export interface UserUpdate {
    /**
     * 
     * @type {string}
     * @memberof UserUpdate
     */
    name?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserUpdate
     */
    firstName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserUpdate
     */
    middleName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserUpdate
     */
    lastName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserUpdate
     */
    phonePin?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UserUpdate
     */
    phoneNumber?: string;
    /**
     * 
     * @type {Array<EntitlementType>}
     * @memberof UserUpdate
     */
    entitlements?: Array<EntitlementType>;
}

/**
 * Check if a given object implements the UserUpdate interface.
 */
export function instanceOfUserUpdate(value: object): value is UserUpdate {
    return true;
}

export function UserUpdateFromJSON(json: any): UserUpdate {
    return UserUpdateFromJSONTyped(json, false);
}

export function UserUpdateFromJSONTyped(json: any, ignoreDiscriminator: boolean): UserUpdate {
    if (json == null) {
        return json;
    }
    return {
        
        'name': json['name'] == null ? undefined : json['name'],
        'firstName': json['first_name'] == null ? undefined : json['first_name'],
        'middleName': json['middle_name'] == null ? undefined : json['middle_name'],
        'lastName': json['last_name'] == null ? undefined : json['last_name'],
        'phonePin': json['phone_pin'] == null ? undefined : json['phone_pin'],
        'phoneNumber': json['phone_number'] == null ? undefined : json['phone_number'],
        'entitlements': json['entitlements'] == null ? undefined : ((json['entitlements'] as Array<any>).map(EntitlementTypeFromJSON)),
    };
}

export function UserUpdateToJSON(value?: UserUpdate | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'name': value['name'],
        'first_name': value['firstName'],
        'middle_name': value['middleName'],
        'last_name': value['lastName'],
        'phone_pin': value['phonePin'],
        'phone_number': value['phoneNumber'],
        'entitlements': value['entitlements'] == null ? undefined : ((value['entitlements'] as Array<any>).map(EntitlementTypeToJSON)),
    };
}

