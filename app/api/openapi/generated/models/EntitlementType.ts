/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * The types of entitlements that can be assigned to a user.
 * @export
 */
export const EntitlementType = {
    MeetingAssistant: 'meeting_assistant',
    ClientIntelligence: 'client_intelligence',
    PracticeIntelligence: 'practice_intelligence',
    MeetingPrep: 'meeting_prep',
    PhoneMeetings: 'phone_meetings',
    OrganizationAdmin: 'organization_admin'
} as const;
export type EntitlementType = typeof EntitlementType[keyof typeof EntitlementType];


export function instanceOfEntitlementType(value: any): boolean {
    for (const key in EntitlementType) {
        if (Object.prototype.hasOwnProperty.call(EntitlementType, key)) {
            if (EntitlementType[key as keyof typeof EntitlementType] === value) {
                return true;
            }
        }
    }
    return false;
}

export function EntitlementTypeFromJSON(json: any): EntitlementType {
    return EntitlementTypeFromJSONTyped(json, false);
}

export function EntitlementTypeFromJSONTyped(json: any, ignoreDiscriminator: boolean): EntitlementType {
    return json as EntitlementType;
}

export function EntitlementTypeToJSON(value?: EntitlementType | null): any {
    return value as any;
}

