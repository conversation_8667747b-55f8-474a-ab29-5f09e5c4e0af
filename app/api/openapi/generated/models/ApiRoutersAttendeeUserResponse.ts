/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface ApiRoutersAttendeeUserResponse
 */
export interface ApiRoutersAttendeeUserResponse {
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersAttendeeUserResponse
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersAttendeeUserResponse
     */
    name: string;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersAttendeeUserResponse
     */
    role?: string | null;
    /**
     * 
     * @type {string}
     * @memberof ApiRoutersAttendeeUserResponse
     */
    phoneNumber?: string;
}

/**
 * Check if a given object implements the ApiRoutersAttendeeUserResponse interface.
 */
export function instanceOfApiRoutersAttendeeUserResponse(value: object): value is ApiRoutersAttendeeUserResponse {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function ApiRoutersAttendeeUserResponseFromJSON(json: any): ApiRoutersAttendeeUserResponse {
    return ApiRoutersAttendeeUserResponseFromJSONTyped(json, false);
}

export function ApiRoutersAttendeeUserResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): ApiRoutersAttendeeUserResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'name': json['name'],
        'role': json['role'] == null ? undefined : json['role'],
        'phoneNumber': json['phone_number'] == null ? undefined : json['phone_number'],
    };
}

export function ApiRoutersAttendeeUserResponseToJSON(value?: ApiRoutersAttendeeUserResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'name': value['name'],
        'role': value['role'],
        'phone_number': value['phoneNumber'],
    };
}

