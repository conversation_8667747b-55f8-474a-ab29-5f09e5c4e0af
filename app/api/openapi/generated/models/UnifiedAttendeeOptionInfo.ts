/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { AttendeeType } from './AttendeeType';
import {
    AttendeeTypeFromJSON,
    AttendeeTypeFromJSONTyped,
    AttendeeTypeToJSON,
} from './AttendeeType';

/**
 * 
 * @export
 * @interface UnifiedAttendeeOptionInfo
 */
export interface UnifiedAttendeeOptionInfo {
    /**
     * 
     * @type {AttendeeType}
     * @memberof UnifiedAttendeeOptionInfo
     */
    type: AttendeeType;
    /**
     * 
     * @type {string}
     * @memberof UnifiedAttendeeOptionInfo
     */
    name: string;
    /**
     * 
     * @type {string}
     * @memberof UnifiedAttendeeOptionInfo
     */
    uuid?: string | null;
    /**
     * 
     * @type {string}
     * @memberof UnifiedAttendeeOptionInfo
     */
    phoneNumber?: string;
}



/**
 * Check if a given object implements the UnifiedAttendeeOptionInfo interface.
 */
export function instanceOfUnifiedAttendeeOptionInfo(value: object): value is UnifiedAttendeeOptionInfo {
    if (!('type' in value) || value['type'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function UnifiedAttendeeOptionInfoFromJSON(json: any): UnifiedAttendeeOptionInfo {
    return UnifiedAttendeeOptionInfoFromJSONTyped(json, false);
}

export function UnifiedAttendeeOptionInfoFromJSONTyped(json: any, ignoreDiscriminator: boolean): UnifiedAttendeeOptionInfo {
    if (json == null) {
        return json;
    }
    return {
        
        'type': AttendeeTypeFromJSON(json['type']),
        'name': json['name'],
        'uuid': json['uuid'] == null ? undefined : json['uuid'],
        'phoneNumber': json['phone_number'] == null ? undefined : json['phone_number'],
    };
}

export function UnifiedAttendeeOptionInfoToJSON(value?: UnifiedAttendeeOptionInfo | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'type': AttendeeTypeToJSON(value['type']),
        'name': value['name'],
        'uuid': value['uuid'],
        'phone_number': value['phoneNumber'],
    };
}

