/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { Transcript } from './Transcript';
import {
    TranscriptFromJSON,
    TranscriptFromJSONTyped,
    TranscriptToJSON,
} from './Transcript';
import type { NoteType } from './NoteType';
import {
    NoteTypeFromJSON,
    NoteTypeFromJSONTyped,
    NoteTypeToJSON,
} from './NoteType';
import type { ActionItem } from './ActionItem';
import {
    ActionItemFromJSON,
    ActionItemFromJSONTyped,
    ActionItemToJSON,
} from './ActionItem';
import type { AttendeeInfo } from './AttendeeInfo';
import {
    AttendeeInfo<PERSON>romJSON,
    AttendeeInfoFromJSONTyped,
    AttendeeInfoToJSON,
} from './AttendeeInfo';
import type { ApiRoutersNoteModelsClient } from './ApiRoutersNoteModelsClient';
import {
    ApiRoutersNoteModelsClientFromJSON,
    ApiRoutersNoteModelsClientFromJSONTyped,
    ApiRoutersNoteModelsClientToJSON,
} from './ApiRoutersNoteModelsClient';
import type { Summary } from './Summary';
import {
    SummaryFromJSON,
    SummaryFromJSONTyped,
    SummaryToJSON,
} from './Summary';
import type { FollowUp } from './FollowUp';
import {
    FollowUpFromJSON,
    FollowUpFromJSONTyped,
    FollowUpToJSON,
} from './FollowUp';
import type { ProcessingStatus } from './ProcessingStatus';
import {
    ProcessingStatusFromJSON,
    ProcessingStatusFromJSONTyped,
    ProcessingStatusToJSON,
} from './ProcessingStatus';

/**
 * 
 * @export
 * @interface NoteResponse
 */
export interface NoteResponse {
    /**
     * 
     * @type {string}
     * @memberof NoteResponse
     */
    uuid: string;
    /**
     * 
     * @type {Date}
     * @memberof NoteResponse
     */
    created: Date;
    /**
     * 
     * @type {Date}
     * @memberof NoteResponse
     */
    modified: Date;
    /**
     * 
     * @type {NoteType}
     * @memberof NoteResponse
     */
    noteType: NoteType | null;
    /**
     * 
     * @type {ProcessingStatus}
     * @memberof NoteResponse
     */
    status: ProcessingStatus;
    /**
     * 
     * @type {boolean}
     * @memberof NoteResponse
     */
    isPrivate: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof NoteResponse
     */
    canUpdatePrivacyStatus: boolean;
    /**
     * 
     * @type {string}
     * @memberof NoteResponse
     */
    meetingName: string;
    /**
     * 
     * @type {number}
     * @memberof NoteResponse
     */
    meetingDurationSeconds: number;
    /**
     * 
     * @type {string}
     * @memberof NoteResponse
     */
    meetingTypeUuid: string | null;
    /**
     * 
     * @type {string}
     * @memberof NoteResponse
     */
    meetingCategory: string;
    /**
     * 
     * @type {Array<AttendeeInfo>}
     * @memberof NoteResponse
     */
    attendees?: Array<AttendeeInfo>;
    /**
     * 
     * @type {Array<string>}
     * @memberof NoteResponse
     */
    tags?: Array<string>;
    /**
     * 
     * @type {Array<ActionItem>}
     * @memberof NoteResponse
     */
    actionItems?: Array<ActionItem>;
    /**
     * 
     * @type {Array<string>}
     * @memberof NoteResponse
     */
    advisorNotes?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof NoteResponse
     */
    keyTakeaways?: Array<string> | null;
    /**
     * 
     * @type {ApiRoutersNoteModelsClient}
     * @memberof NoteResponse
     */
    client?: ApiRoutersNoteModelsClient | null;
    /**
     * 
     * @type {Transcript}
     * @memberof NoteResponse
     */
    transcript: Transcript;
    /**
     * 
     * @type {Summary}
     * @memberof NoteResponse
     */
    summaryByTopics?: Summary;
    /**
     * 
     * @type {string}
     * @memberof NoteResponse
     */
    botId?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof NoteResponse
     */
    isDeleted: boolean;
    /**
     * 
     * @type {Array<string>}
     * @memberof NoteResponse
     */
    features?: Array<string>;
    /**
     * 
     * @type {Array<FollowUp>}
     * @memberof NoteResponse
     */
    followUps?: Array<FollowUp>;
    /**
     * 
     * @type {Array<string>}
     * @memberof NoteResponse
     */
    authorizedUserUuids?: Array<string>;
    /**
     * 
     * @type {string}
     * @memberof NoteResponse
     */
    interactionUuid?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof NoteResponse
     */
    timesEditable: boolean;
    /**
     * 
     * @type {Date}
     * @memberof NoteResponse
     */
    scheduledStartTime?: Date | null;
    /**
     * 
     * @type {Date}
     * @memberof NoteResponse
     */
    scheduledEndTime?: Date | null;
}



/**
 * Check if a given object implements the NoteResponse interface.
 */
export function instanceOfNoteResponse(value: object): value is NoteResponse {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('created' in value) || value['created'] === undefined) return false;
    if (!('modified' in value) || value['modified'] === undefined) return false;
    if (!('noteType' in value) || value['noteType'] === undefined) return false;
    if (!('status' in value) || value['status'] === undefined) return false;
    if (!('isPrivate' in value) || value['isPrivate'] === undefined) return false;
    if (!('canUpdatePrivacyStatus' in value) || value['canUpdatePrivacyStatus'] === undefined) return false;
    if (!('meetingName' in value) || value['meetingName'] === undefined) return false;
    if (!('meetingDurationSeconds' in value) || value['meetingDurationSeconds'] === undefined) return false;
    if (!('meetingTypeUuid' in value) || value['meetingTypeUuid'] === undefined) return false;
    if (!('meetingCategory' in value) || value['meetingCategory'] === undefined) return false;
    if (!('transcript' in value) || value['transcript'] === undefined) return false;
    if (!('isDeleted' in value) || value['isDeleted'] === undefined) return false;
    if (!('timesEditable' in value) || value['timesEditable'] === undefined) return false;
    return true;
}

export function NoteResponseFromJSON(json: any): NoteResponse {
    return NoteResponseFromJSONTyped(json, false);
}

export function NoteResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): NoteResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'created': (new Date(json['created'])),
        'modified': (new Date(json['modified'])),
        'noteType': NoteTypeFromJSON(json['note_type']),
        'status': ProcessingStatusFromJSON(json['status']),
        'isPrivate': json['is_private'],
        'canUpdatePrivacyStatus': json['can_update_privacy_status'],
        'meetingName': json['meeting_name'],
        'meetingDurationSeconds': json['meeting_duration_seconds'],
        'meetingTypeUuid': json['meeting_type_uuid'],
        'meetingCategory': json['meeting_category'],
        'attendees': json['attendees'] == null ? undefined : ((json['attendees'] as Array<any>).map(AttendeeInfoFromJSON)),
        'tags': json['tags'] == null ? undefined : json['tags'],
        'actionItems': json['action_items'] == null ? undefined : ((json['action_items'] as Array<any>).map(ActionItemFromJSON)),
        'advisorNotes': json['advisor_notes'] == null ? undefined : json['advisor_notes'],
        'keyTakeaways': json['key_takeaways'] == null ? undefined : json['key_takeaways'],
        'client': json['client'] == null ? undefined : ApiRoutersNoteModelsClientFromJSON(json['client']),
        'transcript': TranscriptFromJSON(json['transcript']),
        'summaryByTopics': json['summary_by_topics'] == null ? undefined : SummaryFromJSON(json['summary_by_topics']),
        'botId': json['bot_id'] == null ? undefined : json['bot_id'],
        'isDeleted': json['is_deleted'],
        'features': json['features'] == null ? undefined : json['features'],
        'followUps': json['follow_ups'] == null ? undefined : ((json['follow_ups'] as Array<any>).map(FollowUpFromJSON)),
        'authorizedUserUuids': json['authorized_user_uuids'] == null ? undefined : json['authorized_user_uuids'],
        'interactionUuid': json['interaction_uuid'] == null ? undefined : json['interaction_uuid'],
        'timesEditable': json['times_editable'],
        'scheduledStartTime': json['scheduled_start_time'] == null ? undefined : (new Date(json['scheduled_start_time'])),
        'scheduledEndTime': json['scheduled_end_time'] == null ? undefined : (new Date(json['scheduled_end_time'])),
    };
}

export function NoteResponseToJSON(value?: NoteResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'created': ((value['created']).toISOString()),
        'modified': ((value['modified']).toISOString()),
        'note_type': NoteTypeToJSON(value['noteType']),
        'status': ProcessingStatusToJSON(value['status']),
        'is_private': value['isPrivate'],
        'can_update_privacy_status': value['canUpdatePrivacyStatus'],
        'meeting_name': value['meetingName'],
        'meeting_duration_seconds': value['meetingDurationSeconds'],
        'meeting_type_uuid': value['meetingTypeUuid'],
        'meeting_category': value['meetingCategory'],
        'attendees': value['attendees'] == null ? undefined : ((value['attendees'] as Array<any>).map(AttendeeInfoToJSON)),
        'tags': value['tags'],
        'action_items': value['actionItems'] == null ? undefined : ((value['actionItems'] as Array<any>).map(ActionItemToJSON)),
        'advisor_notes': value['advisorNotes'],
        'key_takeaways': value['keyTakeaways'],
        'client': ApiRoutersNoteModelsClientToJSON(value['client']),
        'transcript': TranscriptToJSON(value['transcript']),
        'summary_by_topics': SummaryToJSON(value['summaryByTopics']),
        'bot_id': value['botId'],
        'is_deleted': value['isDeleted'],
        'features': value['features'],
        'follow_ups': value['followUps'] == null ? undefined : ((value['followUps'] as Array<any>).map(FollowUpToJSON)),
        'authorized_user_uuids': value['authorizedUserUuids'],
        'interaction_uuid': value['interactionUuid'],
        'times_editable': value['timesEditable'],
        'scheduled_start_time': value['scheduledStartTime'] == null ? undefined : ((value['scheduledStartTime'] as any).toISOString()),
        'scheduled_end_time': value['scheduledEndTime'] == null ? undefined : ((value['scheduledEndTime'] as any).toISOString()),
    };
}

