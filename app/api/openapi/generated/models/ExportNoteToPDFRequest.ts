/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { CRMSyncItemSelection } from './CRMSyncItemSelection';
import {
    CRMSyncItemSelectionFromJSON,
    CRMSyncItemSelectionFromJSONTyped,
    CRMSyncItemSelectionToJSON,
} from './CRMSyncItemSelection';

/**
 * 
 * @export
 * @interface ExportNoteToPDFRequest
 */
export interface ExportNoteToPDFRequest {
    /**
     * 
     * @type {{ [key: string]: CRMSyncItemSelection; }}
     * @memberof ExportNoteToPDFRequest
     */
    exportItems?: { [key: string]: CRMSyncItemSelection; } | null;
}

/**
 * Check if a given object implements the ExportNoteToPDFRequest interface.
 */
export function instanceOfExportNoteToPDFRequest(value: object): value is ExportNoteToPDFRequest {
    return true;
}

export function ExportNoteToPDFRequestFromJSON(json: any): ExportNoteToPDFRequest {
    return ExportNoteToPDFRequestFromJSONTyped(json, false);
}

export function ExportNoteToPDFRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): ExportNoteToPDFRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'exportItems': json['export_items'] == null ? undefined : (mapValues(json['export_items'], CRMSyncItemSelectionFromJSON)),
    };
}

export function ExportNoteToPDFRequestToJSON(value?: ExportNoteToPDFRequest | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'export_items': value['exportItems'] == null ? undefined : (mapValues(value['exportItems'], CRMSyncItemSelectionToJSON)),
    };
}

