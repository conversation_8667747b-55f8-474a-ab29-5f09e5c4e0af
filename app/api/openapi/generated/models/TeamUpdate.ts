/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * Schema for updating an existing team.
 * @export
 * @interface TeamUpdate
 */
export interface TeamUpdate {
    /**
     * 
     * @type {string}
     * @memberof TeamUpdate
     */
    name?: string | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof TeamUpdate
     */
    memberIds?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof TeamUpdate
     */
    viewerIds?: Array<string> | null;
}

/**
 * Check if a given object implements the TeamUpdate interface.
 */
export function instanceOfTeamUpdate(value: object): value is TeamUpdate {
    return true;
}

export function TeamUpdateFromJSON(json: any): TeamUpdate {
    return TeamUpdateFromJSONTyped(json, false);
}

export function TeamUpdateFromJSONTyped(json: any, ignoreDiscriminator: boolean): TeamUpdate {
    if (json == null) {
        return json;
    }
    return {
        
        'name': json['name'] == null ? undefined : json['name'],
        'memberIds': json['member_ids'] == null ? undefined : json['member_ids'],
        'viewerIds': json['viewer_ids'] == null ? undefined : json['viewer_ids'],
    };
}

export function TeamUpdateToJSON(value?: TeamUpdate | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'name': value['name'],
        'member_ids': value['memberIds'],
        'viewer_ids': value['viewerIds'],
    };
}

