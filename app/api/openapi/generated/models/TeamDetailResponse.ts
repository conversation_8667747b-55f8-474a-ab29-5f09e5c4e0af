/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { OrganizationSummary } from './OrganizationSummary';
import {
    OrganizationSummaryFromJSON,
    OrganizationSummaryFromJSONTyped,
    OrganizationSummaryToJSON,
} from './OrganizationSummary';
import type { UserSummary } from './UserSummary';
import {
    UserSummaryFromJSON,
    UserSummaryFromJSONTyped,
    UserSummaryToJSON,
} from './UserSummary';

/**
 * Detailed response schema for team information including members.
 * @export
 * @interface TeamDetailResponse
 */
export interface TeamDetailResponse {
    /**
     * Team UUID
     * @type {string}
     * @memberof TeamDetailResponse
     */
    uuid: string;
    /**
     * Team name
     * @type {string}
     * @memberof TeamDetailResponse
     */
    name: string;
    /**
     * Organization information
     * @type {OrganizationSummary}
     * @memberof TeamDetailResponse
     */
    organization: OrganizationSummary;
    /**
     * When the team was created
     * @type {Date}
     * @memberof TeamDetailResponse
     */
    created: Date;
    /**
     * When the team was last modified
     * @type {Date}
     * @memberof TeamDetailResponse
     */
    modified: Date;
    /**
     * List of team members
     * @type {Array<UserSummary>}
     * @memberof TeamDetailResponse
     */
    members: Array<UserSummary>;
    /**
     * Users who can view this team
     * @type {Array<UserSummary>}
     * @memberof TeamDetailResponse
     */
    viewers: Array<UserSummary>;
}

/**
 * Check if a given object implements the TeamDetailResponse interface.
 */
export function instanceOfTeamDetailResponse(value: object): value is TeamDetailResponse {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    if (!('organization' in value) || value['organization'] === undefined) return false;
    if (!('created' in value) || value['created'] === undefined) return false;
    if (!('modified' in value) || value['modified'] === undefined) return false;
    if (!('members' in value) || value['members'] === undefined) return false;
    if (!('viewers' in value) || value['viewers'] === undefined) return false;
    return true;
}

export function TeamDetailResponseFromJSON(json: any): TeamDetailResponse {
    return TeamDetailResponseFromJSONTyped(json, false);
}

export function TeamDetailResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): TeamDetailResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'name': json['name'],
        'organization': OrganizationSummaryFromJSON(json['organization']),
        'created': (new Date(json['created'])),
        'modified': (new Date(json['modified'])),
        'members': ((json['members'] as Array<any>).map(UserSummaryFromJSON)),
        'viewers': ((json['viewers'] as Array<any>).map(UserSummaryFromJSON)),
    };
}

export function TeamDetailResponseToJSON(value?: TeamDetailResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'name': value['name'],
        'organization': OrganizationSummaryToJSON(value['organization']),
        'created': ((value['created']).toISOString()),
        'modified': ((value['modified']).toISOString()),
        'members': ((value['members'] as Array<any>).map(UserSummaryToJSON)),
        'viewers': ((value['viewers'] as Array<any>).map(UserSummaryToJSON)),
    };
}

