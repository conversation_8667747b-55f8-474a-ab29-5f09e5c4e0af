import { ConfigurationParameters } from "app/api/openapi/generated";
import { authenticator } from "~/auth/authenticator.server";

// Returns a set of signed-in configuration parameters for a generated API client.
//
// Redirects the user to the login page if they are not authenticated.
export const configurationParameters = async (
  request: Request
): Promise<ConfigurationParameters> => {
  const userAuthSession = await authenticator.isAuthenticated(request, {
    failureRedirect: "/auth/login",
  });

  return {
    basePath: process.env.ZEPLYN_BACKEND_BASE_URL,
    accessToken: userAuthSession.accessToken,
  };
};

// Returns a set of signed-out configuration parameters for a generated API client.
export const nonAuthenticatedConfigurationParameters =
  async (): Promise<ConfigurationParameters> => {
    return { basePath: process.env.ZEPLYN_BACKEND_BASE_URL };
  };
