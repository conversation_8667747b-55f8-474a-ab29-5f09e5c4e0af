import { authenticator } from "~/auth/authenticator.server";

const API_BASE = process.env.ZEPLYN_FRONTEND_API_BASE_URL;
const ROUTE = `/api/v2/bot/:botID/events`;

export type eventsArguments = {
  botID: string;
};

export type eventsResult = {
  eventSourceURL: string;
};

export const getEventStreamParams = async (
  payload: eventsArguments & { request: Request }
): Promise<eventsResult> => {
  const { botID, request } = payload;

  const endpoint = `${API_BASE}${ROUTE}`.replace(":botID", botID);
  const userAuthSession = await authenticator.isAuthenticated(request, {
    failureRedirect: "/auth/login",
  });
  return {
    eventSourceURL: `${endpoint}?access_token=${userAuthSession.accessToken}`,
  };
};
