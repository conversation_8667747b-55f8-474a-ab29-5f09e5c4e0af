import { configurationParameters } from "../openapi/configParams";
import {
  Configuration,
  ListNotesResponse,
  NoteApi,
} from "../openapi/generated";
import { getUserSessionOrRedirect } from "~/auth/authenticator.server";
import { logError } from "~/utils/log.server";

// Exports
export type GetNotesArguments = {
  searchTerm?: string;
  request: Request;
  notBefore?: number;
  notAfter?: number;
  includeTeams?: string[];
};
export const getNotes = async ({
  request,
  searchTerm = "",
  notBefore,
  notAfter,
  includeTeams
}: GetNotesArguments): Promise<ListNotesResponse[]> => {
  try {
    const { userId } = await getUserSessionOrRedirect(request);
    if (!userId) throw Error("Could not read userId from user session");
    const config = new Configuration(await configurationParameters(request));
    console.log("check???")
    return new NoteApi(config).noteListNotes({
      q: searchTerm,
      notBefore,
      notAfter,
      // includeTeams: ["c4a249c3-89bb-4e90-9d72-67d7d5a0ae3c"]
    });
  } catch (error) {
    if (error instanceof Response) throw error;
    // All other errors
    logError("!!! api/notes/getNotes", error);
    throw Error("Something went wrong");
  }
};
