import { createContext, ReactNode, useContext } from "react";

type UiSettings = {
  theme: "light" | "dark";
  navbarCollapsed?: boolean;
  defaultTeamUuidsForSearch?: string[];
};

const UiSettingsContext = createContext<UiSettings>({
  // the following are DISPLAYED in UI settings page
  theme: "light",

  // the following are NOT DISPLAYED in UI settings page
  navbarCollapsed: true,
  defaultTeamUuidsForSearch: [], // teams filter for search results page
});

type Props = { uiSettings: UiSettings; children: ReactNode };
const UiSettingsProvider = ({ uiSettings, children }: Props) => (
  <UiSettingsContext.Provider value={uiSettings}>
    {children}
  </UiSettingsContext.Provider>
);

function useUiSettings() {
  return useContext(UiSettingsContext);
}

export { UiSettingsContext, UiSettingsProvider, useUiSettings };
