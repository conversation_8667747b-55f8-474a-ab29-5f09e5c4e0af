import { NoteCard } from "./NoteCard";
import {
  NoteResponse,
  NoteType,
  ProcessingStatus,
} from "~/api/openapi/generated";
import { render, screen } from "@testing-library/react";
import { createMemoryRouter, RouterProvider } from "react-router";
import { SerializeFrom } from "~/types/remix";

describe("NoteCard", () => {
  const note: SerializeFrom<NoteResponse> = {
    uuid: "1",
    meetingName: "Test Meeting",
    status: ProcessingStatus.Processed,
    noteType: NoteType.MeetingRecording,
    created: new Date("2023-04-01T10:00:00.000Z"),
    modified: new Date("2023-04-01T10:00:00.000Z"),
    meetingDurationSeconds: 10,
    meetingTypeUuid: null,
    meetingCategory: "client",
    actionItems: [],
    advisorNotes: [],
    keyTakeaways: [],
    client: undefined,
    transcript: { utterances: [] },
    summaryByTopics: { sections: [] },
    isDeleted: false,
    botId: undefined,
    features: [],
    timesEditable: false,
    isPrivate: false,
    canUpdatePrivacyStatus: true,
  };

  // Helper function to render component with router context
  const renderWithRouter = (component: React.ReactElement) => {
    const router = createMemoryRouter(
      [
        {
          path: "/",
          element: component,
        },
        {
          path: "/notes/:id",
          element: <div>Note Page</div>,
        },
      ],
      { initialEntries: ["/"] }
    );
    return render(<RouterProvider router={router} />);
  };

  it("renders note details correctly", () => {
    renderWithRouter(<NoteCard note={note} to="/notes/1" />);
    expect(screen.getByText("Test Meeting")).toBeInTheDocument();
    expect(screen.getByText("Created on 04/01/2023")).toBeInTheDocument();
  });

  it("renders scheduled note timestamp correctly", () => {
    const scheduledNote = {
      ...note,
      status: ProcessingStatus.Scheduled,
      scheduledStartTime: new Date("2024-04-01T10:00:00.000Z"),
    };
    renderWithRouter(<NoteCard note={scheduledNote} to="/notes/1" />);
    expect(screen.getByText("Scheduled for 04/01/2024")).toBeInTheDocument();
  });

  it("renders compact version correctly", () => {
    renderWithRouter(<NoteCard note={note} to="/notes/1" compact />);
    expect(screen.getByText("Test Meeting")).toBeInTheDocument();
    expect(screen.queryByText(/Created/)).not.toBeInTheDocument();
  });

  it("renders disabled state correctly", () => {
    const disabledNote = {
      ...note,
      status: ProcessingStatus.Uploaded,
    };
    renderWithRouter(<NoteCard note={disabledNote} to="/notes/1" compact />);
    expect(screen.getByRole("link")).toHaveAttribute("tabindex", "-1");
    expect(screen.getByRole("link")).toHaveClass(
      "pointer-events-none",
      "opacity-50",
      "shadow-none"
    );
  });
});
