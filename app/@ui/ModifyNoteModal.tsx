import React, { useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
} from "~/@shadcn/ui/dialog";
import { Button } from "~/@shadcn/ui/button";
import { TextareaGrowable } from "~/@shadcn/ui/textarea";
import {
  FormField,
  FormLabel,
  FormControl,
  FormMessage,
} from "~/@shadcn/ui/form";
import { useForm } from "react-hook-form";

/* copied from AskAnythingModal.tsx */
const LoadingIndicator = () => (
  <div className="flex items-center space-x-2">
    <div className="h-1.5 w-1.5 animate-bounce rounded-full bg-gray-500"></div>
    <div className="h-1.5 w-1.5 animate-bounce rounded-full bg-gray-500 delay-200"></div>
    <div className="h-1.5 w-1.5 animate-bounce rounded-full bg-gray-500 delay-500"></div>
  </div>
);

interface ModifyNoteWithPromptModalProps {
  open: boolean;
  onOpen: () => void;
  onClose: () => void;
  onSubmit: (prompt: string) => void;
  loading?: boolean;
  error?: string | null;
  clearOnSuccess?: boolean;
}

export const ModifyNoteWithPromptModal: React.FC<
  ModifyNoteWithPromptModalProps
> = ({ open, onOpen, onClose, onSubmit, loading, error, clearOnSuccess }) => {
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isValid, isDirty },
  } = useForm<{ prompt: string }>({
    mode: "onTouched",
    defaultValues: { prompt: "" },
  });

  // Clear prompt when operation succeeds
  useEffect(() => {
    if (clearOnSuccess) {
      reset();
    }
  }, [clearOnSuccess, reset]);

  const onFormSubmit = (data: { prompt: string }) => {
    onSubmit(data.prompt);
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        if (!open) onClose();
        else onOpen();
      }}
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Modify Note with Prompt</DialogTitle>
        </DialogHeader>
        <form
          onSubmit={handleSubmit(onFormSubmit)}
          className="max-h-[60vh] min-h-[15vh]"
        >
          <FormField name="prompt" required error={errors.prompt?.message}>
            <FormLabel>Prompt</FormLabel>
            <FormControl>
              <TextareaGrowable
                {...register("prompt", {
                  required: "Prompt is required.",
                  minLength: {
                    value: 1,
                    message: "Prompt must be at least 1 character.",
                  },
                  maxLength: {
                    value: 2000,
                    message: "Prompt must be at most 2000 characters.",
                  },
                  validate: (v) =>
                    v.trim().length >= 1 || "Prompt must not be empty.",
                })}
                placeholder="Describe how you want the note updated. You will be able to review your changes before deciding whether to save them."
                disabled={loading}
                className="mb-2"
              />
            </FormControl>
            <FormMessage />
          </FormField>
          {error && (
            <div className="mb-2 text-sm text-destructive">{error}</div>
          )}
          {loading && (
            <div className="mb-2 flex items-center space-x-2 text-sm">
              <span>Modifying</span>
              <div className="scale-75 opacity-75">
                <LoadingIndicator />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button
              type="button"
              variant="ghost"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="outline_magic"
              disabled={!isValid || loading}
            >
              {loading ? "Modifying with Prompt..." : "Modify Note with Prompt"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
