import { FormControl, FormField, FormLabel } from "~/@shadcn/ui/form";
import { cn } from "~/@shadcn/utils";
import { AssigneeType } from "~/api/openapi/generated";

type ActionItemAssigneeTypePickerProps = {
  id: string;
  name: string;
  assigneeType?: AssigneeType | null;
  onSelect?: (nextType: AssigneeType | undefined) => void;
  triggerClassName?: string;
};

const actionItemAssigneeTypeOptions = [
  {
    label: "Advisor Task",
    value: AssigneeType.AdvisorTask,
  },
  {
    label: "Client Follow-up",
    value: AssigneeType.ClientFollowup,
  },
];

export const ActionItemAssigneeTypePicker = ({
  id,
  name,
  assigneeType,
  onSelect,
  triggerClassName,
}: ActionItemAssigneeTypePickerProps) => {
  const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const value = event.target.value;
    if (value === "") {
      onSelect?.(undefined);
    } else {
      onSelect?.(value as AssigneeType);
    }
  };

  return (
    <FormField id={id} name={name}>
      <FormControl>
        <select
          value={assigneeType || ""}
          onChange={handleChange}
          className={cn(
            "h-auto rounded-2xl border border-input bg-background p-3 text-sm ring-offset-background",
            "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
            "disabled:cursor-not-allowed disabled:opacity-50",
            !assigneeType && "text-muted-foreground",
            triggerClassName
          )}
        >
          <option value="" disabled>
            Pick type
          </option>
          {actionItemAssigneeTypeOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </FormControl>
    </FormField>
  );
};
