import { ContactIcon, HelpCircleIcon, Plus, UserIcon, X } from "lucide-react";

import { Badge } from "~/@shadcn/ui/badge";
import { AttendeeOption } from "~/api/attendees/types";
import { Button } from "~/@shadcn/ui/button";
import { AttendeeInfo } from "~/api/openapi/generated";
import { Mouse<PERSON>ventHandler } from "react";

const tagOptions = {
  client: {
    style: "bg-primary text-primary-foreground",
    icon: (
      <ContactIcon
        className="!h-4 !w-4 shrink-0"
        aria-label="Client"
        data-testid="ContactIcon"
      />
    ),
  },
  user: {
    style: "bg-warning-foreground text-warning",
    icon: (
      <UserIcon
        className="!h-4 !w-4 shrink-0"
        aria-label="User"
        data-testid="UserIcon"
      />
    ),
  },
  unknown: {
    style: "bg-secondary text-primary-foreground",
    icon: (
      <HelpCircleIcon
        className="!h-4 !w-4 shrink-0"
        aria-label="Unknown attendee"
        data-testid="HelpCircleIcon"
      />
    ),
  },
};

export const AttendeeTag = (attendee: AttendeeInfo) => {
  const { type } = attendee;

  // Ensure the type is valid
  if (!type || !(type in tagOptions)) {
    return null;
  }

  return (
    <Badge
      className={`${tagOptions[type].style} mt-0 flex h-6 items-center p-1 text-xs`}
      key={attendee.uuid}
      inline="inline"
      variant={"secondary"}
    >
      <div className="flex items-center gap-0.5">
        {tagOptions[type].icon}
        {attendee.name}
        {attendee.speakerPercentage && attendee.speakerPercentage > 0
          ? ` (${attendee.speakerPercentage}%)`
          : null}
      </div>
    </Badge>
  );
};

export const AttendeeOptionTag = ({
  attendeeOption,
  onDelete,
  onClick,
}: {
  attendeeOption: AttendeeOption;
  onDelete: () => void;
  onClick: MouseEventHandler<HTMLDivElement>;
}) => {
  const { type } = attendeeOption;

  // Ensure the type is valid
  if (!type || !(type in tagOptions)) {
    return null;
  }

  return (
    <Badge
      className={`${tagOptions[type].style} max-w-full`}
      key={attendeeOption.uuid}
      inline="inline"
      variant={"secondary"}
      onClick={onClick}
    >
      <div className="flex max-w-[calc(100%-20px)] flex-row items-center gap-1">
        {tagOptions[type].icon}
        <span className="truncate">{attendeeOption.name}</span>
      </div>

      {/* show + icon if type isn't client or user */}
      {type !== "client" && type !== "user" && (
        <Plus size={18} className="ml-1" />
      )}

      <Button
        className="h-5 w-5 [&>svg]:h-4 [&>svg]:w-4"
        size="icon-xs"
        variant="ghost"
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          onDelete();
        }}
        asChild
      >
        <span role="button">
          <X />
        </span>
      </Button>
    </Badge>
  );
};
