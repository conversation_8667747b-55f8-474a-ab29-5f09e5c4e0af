import { Link, useLocation } from "react-router";
import { ReactNode } from "react";
import {
  ChartNoAxesCombined,
  House,
  ListChecks,
  LogOut,
  NotepadText,
  Search,
  Settings,
  Users,
} from "lucide-react";

import { Button } from "~/@shadcn/ui/button";
import { CompanyIcon } from "~/@ui/assets/CompanyIcon";
import { CreateButton } from "~/@ui/buttons/CreateButton";
import { useFlag } from "~/context/flags";

type NavConfig = {
  matcher: (path: string) => boolean;
  config: {
    title: string;
    createPath?: string;
    createButtonTooltip?: string;
    showLogoutButton?: boolean;
    icon: React.ComponentType<React.ComponentProps<"svg">>;
  };
};

export const TopNavBar = ({ rightChild }: { rightChild?: ReactNode }) => {
  const location = useLocation();
  const isInsightsDashboardEnabled =
    useFlag("EnablePracticeInsightsDashboard") ?? false;
  const isCollapsibleNavbarEnabled = useFlag("EnableCollapsibleNavbar");
  const isClientCreationEnabled = useFlag("EnableClientCreation");

  const currentPath = location.pathname;

  const routeConfigs: NavConfig[] = [
    {
      matcher: (path) => path.startsWith("/dashboard"),
      config: {
        title: "Advisor Hub",
        createPath: "/notes/create",
        createButtonTooltip: "Create note",
        icon: House,
      },
    },
    {
      matcher: (path) =>
        isInsightsDashboardEnabled && path.startsWith("/insights"),
      config: {
        title: "Insights",
        icon: ChartNoAxesCombined,
      },
    },
    {
      matcher: (path) => path.startsWith("/notes"),
      config: {
        title: "Notes",
        createPath: "/notes/create",
        createButtonTooltip: "Create note",
        icon: NotepadText,
      },
    },
    {
      matcher: (path) => path.startsWith("/clients"),
      config: {
        title: "Clients",
        createPath: isClientCreationEnabled ? "?action=create" : "",
        createButtonTooltip: isClientCreationEnabled ? "Create client" : "",
        icon: Users,
      },
    },
    {
      matcher: (path) => path.startsWith("/tasks"),
      config: {
        title: "Tasks",
        createPath: "/tasks/create",
        createButtonTooltip: "Create task",
        icon: ListChecks,
      },
    },
    {
      matcher: (path) => path.startsWith("/settings"),
      config: {
        title: "Settings",
        showLogoutButton: true,
        icon: Settings,
      },
    },
    {
      matcher: (path) => path.startsWith("/search"),
      config: {
        title: "Search Results",
        showLogoutButton: true,
        icon: Search,
      },
    },
  ];

  const matchedConfig = routeConfigs.find(({ matcher }) =>
    matcher(currentPath)
  )?.config;

  const {
    title,
    createPath,
    createButtonTooltip,
    showLogoutButton,
    icon: Icon,
  } = matchedConfig || {
    title: "Your notes",
    createPath: "/notes/create",
    createButtonTooltip: "Create note",
    showLogoutButton: false,
  };

  return (
    <div className="relative flex w-full justify-between border-b border-gray-200 px-4 md:py-2">
      <div className="flex items-center">
        {isCollapsibleNavbarEnabled ? (
          Icon && <Icon />
        ) : (
          <Link to="/" className="flex items-center">
            <CompanyIcon className="h-10 w-10 text-blue-600" />
          </Link>
        )}
        <h1 className="ml-2 text-2xl font-semibold">{title}</h1>
      </div>

      <div className="flex items-center">
        {createPath && (
          <div className="hidden md:block">
            <CreateButton
              label="Create"
              to={createPath}
              tooltip={createButtonTooltip}
            />
          </div>
        )}
        {showLogoutButton && (
          <Button
            className="hidden md:flex md:items-center"
            variant="outline"
            asChild
          >
            <Link to="/auth/logout" className="">
              <LogOut />
              <span>Logout</span>
            </Link>
          </Button>
        )}
        {rightChild}
      </div>
    </div>
  );
};
