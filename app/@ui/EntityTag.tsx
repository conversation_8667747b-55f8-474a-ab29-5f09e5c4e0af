import React from "react";

import { But<PERSON> } from "~/@shadcn/ui/button";
import { cn } from "~/@shadcn/utils";
import { Badge } from "~/@shadcn/ui/badge";

type EntityTagProps = {
  title: string;
  onClick: () => void;
  Icon?: React.ElementType;
  children: React.ReactNode;

  badgeStyle?: string;
  BadgeIcon?: React.ElementType;
  badgeText?: string;

  suffixContent?: React.ReactNode;
};
const EntityTag = ({
  title,
  onClick,
  Icon,
  children,
  badgeStyle,
  BadgeIcon,
  badgeText,
  suffixContent,
}: EntityTagProps) => {
  return (
    <Button
      className={cn(
        "w-full gap-2 overflow-hidden text-ellipsis whitespace-nowrap [&>svg]:h-4 [&>svg]:w-4"
      )}
      variant="ghost"
      onClick={onClick}
      title={title}
    >
      {Icon && <Icon size={15} className="shrink-0" />}

      <span className="grow overflow-hidden text-ellipsis whitespace-nowrap text-start">
        {children}
      </span>

      {badgeText && (
        <Badge className={badgeStyle} inline="inline">
          <div className="flex flex-row gap-0.5">
            {BadgeIcon && <BadgeIcon />}
            {badgeText}
          </div>
        </Badge>
      )}

      {suffixContent}
    </Button>
  );
};

export default EntityTag;
