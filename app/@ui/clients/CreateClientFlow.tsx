import { useEffect, useMemo, useState } from "react";
import { RotateCcw, Save } from "lucide-react";
import PhoneInputWithCountrySelect from "react-phone-number-input";

import { But<PERSON> } from "~/@shadcn/ui/button";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from "~/@shadcn/ui/sheet";
import { FormField, FormLabel } from "~/@shadcn/ui/form";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/@shadcn/ui/select";
import { Input } from "~/@shadcn/ui/input";
import {
  ClientApi,
  ClientType,
  CreateClientRequest,
  ResponseError,
} from "~/api/openapi/generated";
import { capitalize } from "~/utils/strings";

import "react-phone-number-input/style.css";
import { useAPIConfiguration } from "~/context/apiAuth";
import { datadogLogs } from "@datadog/browser-logs";

type PrefilledDetails = {
  name?: string;
  email?: string;
  text?: string;
};

type Props = {
  onOpenChange: (isOpen: boolean) => void;
  onCreate?: (clientUuid: string, formData: Record<string, any>) => void;
  onError?: (msg: string) => void;
  prefilledDetails?: PrefilledDetails;
  successMsg?: string;
};

const CreateClientFlow = (props: Props) => {
  const { onOpenChange, onCreate, onError, prefilledDetails, successMsg } =
    props;

  const [formData, setFormData] = useState<
    Record<keyof CreateClientRequest, string>
  >(initializeData(prefilledDetails));
  const [isInvalid, setIsInvalid] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isSaved, setIsSaved] = useState(false);

  const apiConfig = useAPIConfiguration();
  const clientAPIClient = useMemo(() => new ClientApi(apiConfig), [apiConfig]);

  // if any required fields are empty; disable CTA
  useEffect(() => {
    const hasEmptyRequiredFields = getFormFields().some(
      (field) => field.required && !formData[field.id]
    );
    setIsInvalid(hasEmptyRequiredFields);
  }, [formData]);

  const onSave = async () => {
    setIsSaving(true);

    try {
      const { clientUuid } = await clientAPIClient.clientCreateClient({
        createClientRequest: {
          ...formData,
          dateOfBirth: formData.dateOfBirth
            ? new Date(formData.dateOfBirth)
            : undefined,
          onboardingDate: formData.onboardingDate
            ? new Date(formData.onboardingDate)
            : undefined,
          phoneNumber: formData.phoneNumber || undefined,
          type: formData.type ? (formData.type as ClientType) : undefined,
        },
      });

      setIsSaved(true);
      onCreate?.(clientUuid, formData);
    } catch (e) {
      datadogLogs.logger.error("Failed to create client", { error: e });
      if (onError) {
        if (e instanceof ResponseError) {
          onError(
            e.response.status === 400
              ? "Cannot create client: a client with the same email already exists"
              : "An error occurred. Failed to create client"
          );
        } else if (typeof e === "object" && e !== null && "message" in e) {
          onError(e.message as string);
        }
      }
    } finally {
      setIsSaving(false);
    }
  };

  // reset form data to initial values (computed from prefilled details)
  const onReset = () => {
    setFormData(initializeData(prefilledDetails));
  };

  return (
    <Sheet open onOpenChange={onOpenChange}>
      <SheetContent className="flex w-full flex-col">
        <SheetHeader>
          <SheetTitle>Create a New Client</SheetTitle>
          <SheetDescription />
        </SheetHeader>

        <div className="flex grow flex-col justify-start gap-4 overflow-auto px-0.5">
          {getFormFields().map(
            ({ id, label, type, options, required, disabled }) => {
              let inputComponent = null;
              switch (type) {
                case "text":
                  inputComponent = (
                    <Input
                      value={formData[id]}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          [id]: e.target.value,
                        })
                      }
                    />
                  );
                  break;
                case "phone":
                  inputComponent = (
                    <PhoneInputWithCountrySelect
                      defaultCountry="US"
                      placeholder="Enter phone number..."
                      className="flex w-full cursor-text items-center gap-1 rounded-2xl border border-border bg-background p-3 shadow transition-colors placeholder:text-secondary hover:border-foreground hover:bg-accent has-[input:read-only]:pointer-events-none has-[button:hover]:bg-background has-[input:focus]:outline-none has-[input:focus]:ring-2 has-[input:focus]:ring-ring"
                      numberInputProps={{
                        className:
                          "w-full flex bg-transparent shadow-none hover:bg-transparent has-[input:focus]:ring-0 transition-colors placeholder:text-secondary focus-visible:outline-none",
                      }}
                      value={formData.phoneNumber}
                      onChange={(value) =>
                        setFormData({
                          ...formData,
                          phoneNumber: value || "",
                        })
                      }
                    />
                  );
                  break;
                case "dropdown":
                  inputComponent = (
                    <Select
                      onValueChange={(value) =>
                        setFormData({
                          ...formData,
                          [id]: value,
                        })
                      }
                      value={formData[id]}
                      disabled={disabled}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select an option" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          {options?.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  );
                  break;
                case "date":
                  inputComponent = (
                    <Input
                      type="date"
                      value={formData[id]}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          [id]: e.target.value,
                        })
                      }
                    />
                  );
                  break;
              }
              return (
                <FormField key={id} id={id} name={id} required={required}>
                  <FormLabel>{label}</FormLabel>
                  {inputComponent}
                </FormField>
              );
            }
          )}
        </div>

        <SheetFooter className="shrink-0 flex-col gap-y-2 sm:justify-start">
          {isSaved && successMsg && (
            <div className="text-sm text-success">{successMsg}</div>
          )}

          {!(isSaved && successMsg) && (
            <>
              <Button onClick={onSave} disabled={isInvalid || isSaving}>
                <Save />
                {isSaving ? "Creating..." : "Create Client"}
              </Button>
              <Button onClick={onReset} variant="ghost" disabled={isSaving}>
                <RotateCcw />
                Reset
              </Button>
            </>
          )}
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

function initializeData(prefilledDetails?: PrefilledDetails) {
  const { name, email, text } = prefilledDetails || {};

  const data = {
    ...getFormFields().reduce(
      (acc, { id, defaultValue }) => {
        acc[id] = defaultValue || "";
        return acc;
      },
      {} as Record<keyof CreateClientRequest, string>
    ),
    name: name || "",
    email: email || "",
  };

  // extract name / email from `text`
  if (text) {
    if (text.includes("@")) {
      data.email = text;
    } else {
      data.name = text;
    }
  }

  return data;
}

type FormField<T> = {
  id: keyof T;
  label: string;
  type: "text" | "phone" | "dropdown" | "date";
  required?: boolean;
  options?: { label: string; value: string }[];
  defaultValue?: string;
  disabled?: boolean;
};

function getFormFields(): FormField<CreateClientRequest>[] {
  return [
    {
      id: "name",
      label: "Preferred Name",
      type: "text",
    },
    {
      id: "firstName",
      label: "First Name",
      type: "text",
    },
    {
      id: "lastName",
      label: "Last Name",
      type: "text",
    },
    {
      id: "dateOfBirth",
      label: "Date of Birth",
      type: "date",
    },
    {
      id: "jobTitle",
      label: "Job Title",
      type: "text",
    },
    {
      id: "email",
      label: "Email",
      type: "text",
      required: true,
    },
    {
      id: "phoneNumber",
      label: "Phone",
      type: "phone",
    },
    {
      id: "type",
      label: "Client Type",
      type: "dropdown",
      options: [
        {
          label: capitalize(ClientType.Individual),
          value: ClientType.Individual,
        },
        {
          label: capitalize(ClientType.Household),
          value: ClientType.Household,
        },
      ],
      defaultValue: "individual",
      disabled: true,
    },
    {
      id: "onboardingDate",
      label: "Onboarding Date",
      type: "date",
    },
  ];
}

export default CreateClientFlow;
