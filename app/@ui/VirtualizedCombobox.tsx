import React, { useState, useEffect } from "react";
import { But<PERSON> } from "~/@shadcn/ui/button";
import {
  Command,
  CommandInput,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from "~/@shadcn/ui/command";
import { Popover, PopoverTrigger, PopoverContent } from "~/@shadcn/ui/popover";
import { cn } from "~/@shadcn/utils";
import { CaretSortIcon } from "@radix-ui/react-icons";
import { Typography } from "~/@ui/Typography";
import { FixedSizeList } from "react-window";
import { useDebounce } from "~/utils/useDebounce";

type OptionType = {
  label: string;
  value: string;
  row: React.ReactNode;
};

type Props = {
  options: OptionType[];

  // NOTE: the entire object is being tracked since `value` is not guaranteed to be a part of `options` array
  // Reason #1: `option` containing the selected `value` might not have loaded yet
  // Reason #2: filtered options (from search term) no longer contains the "selected value"
  selectedObject?: OptionType;

  placeholder?: string;
  onChange: (value: OptionType | undefined) => void;
  commandClassName?: string;
  triggerClassName?: string;
  leftIcon?: React.ReactNode;
  searchOnLabel?: boolean;
  disabled?: boolean;
  modal?: boolean;
  loadOptions: (debouncedSearchTerm: string) => Promise<void>;
  itemSize?: number;
  maxHeightPx?: number;
};

export const VirtualizedCombobox = ({
  options,
  selectedObject,
  placeholder = "Select an item",
  onChange,
  commandClassName,
  triggerClassName,
  leftIcon,
  searchOnLabel = false,
  disabled = false,
  modal = false,
  loadOptions,
  itemSize = 40,
  maxHeightPx = 300,
  ...props
}: Props) => {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const debouncedSearchTerm = useDebounce(searchTerm, 400);

  useEffect(() => {
    open && loadOptions(debouncedSearchTerm);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, debouncedSearchTerm]);

  const { label: selectedLabel, value: selectedValue } = selectedObject || {};

  // Virtualized row renderer
  const Row = ({
    index,
    style,
  }: {
    index: number;
    style: React.CSSProperties;
  }) => {
    const option = options[index];
    if (!option) return null;

    return (
      <div style={style}>
        <CommandItem
          key={option.value}
          className="gap-2"
          value={option.value}
          onSelect={(currentValue: string) => {
            if (disabled) {
              return;
            }
            onChange(
              currentValue === selectedValue
                ? undefined
                : options.find((o) => o.value === currentValue)
            );
            setOpen(false);
          }}
        >
          {option.row}
        </CommandItem>
      </div>
    );
  };

  return (
    <Popover open={open} onOpenChange={setOpen} modal={modal} {...props}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between gap-2 rounded-2xl p-3 text-base",
            "h-fit min-h-fit",
            triggerClassName
          )}
          disabled={disabled}
        >
          {leftIcon}
          {selectedValue ? (
            <span className="grow overflow-hidden text-ellipsis whitespace-nowrap text-start">
              {selectedLabel}
            </span>
          ) : (
            <Typography className="inline-flex grow" color="secondary" asChild>
              <span>{placeholder}</span>
            </Typography>
          )}
          <CaretSortIcon className="h-6 w-6 shrink-0 self-baseline" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className={cn(
          "w-fit min-w-[var(--radix-popper-anchor-width)] p-0",
          commandClassName
        )}
      >
        <Command
          className={commandClassName}
          shouldFilter={false} // Disable client-side filtering
        >
          <CommandInput
            placeholder="Search..."
            value={searchTerm}
            onValueChange={setSearchTerm}
          />
          <CommandList>
            <CommandEmpty>No item found.</CommandEmpty>
            <CommandGroup>
              {options.length > 0 && (
                <FixedSizeList
                  height={Math.min(options.length * itemSize, maxHeightPx)}
                  width="100%"
                  itemCount={options.length}
                  itemSize={itemSize}
                  onItemsRendered={({
                    visibleStopIndex,
                  }: {
                    visibleStopIndex: number;
                  }) => {
                    // Trigger loadMore when user scrolls near the end
                    if (visibleStopIndex >= options.length - 5) {
                      loadOptions(debouncedSearchTerm);
                    }
                  }}
                >
                  {Row}
                </FixedSizeList>
              )}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};
