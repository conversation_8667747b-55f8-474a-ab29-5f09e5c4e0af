// @vitest-environment node

import { describe, it, expect, vi } from "vitest";
import { action, loader } from "./route";
import { getAttendeeOptions } from "~/api/attendees/getAttendeeOptions.server";
import { getNoteById } from "~/api/notes/getNoteById.server";
import { getUserSessionOrRedirect } from "~/auth/authenticator.server";
import { AttendeeOptionsStruct } from "~/api/attendees/types";
import { v4 as uuidv4 } from "uuid";
import {
  AttendeeType,
  BotApi,
  BotMeetingType,
  BotStatus,
  NoteApi,
  NoteType,
  ProcessingStatus,
} from "~/api/openapi/generated";
import { configurationParameters } from "~/api/openapi/configParams";
import { ActionTypes } from "~/utils/const";
import { redirect } from "react-router";
import safeBtoa from "~/utils/safeBtoa";

describe("loader", () => {
  const mockFetch = vi.fn();

  beforeEach(() => {
    vi.mock("~/api/attendees/getAttendeeOptions.server");
    vi.mock("~/api/notes/getNoteById.server");
    vi.mock("~/api/bot/getNoteById.server");
    vi.mock("~/auth/authenticator.server");
    vi.mock("~/api/openapi/configParams");

    vi.mocked(configurationParameters).mockResolvedValue({
      fetchApi: mockFetch,
    });
    mockFetch.mockResolvedValue(
      new Response(
        JSON.stringify({
          meeting_types: [],
          default_meeting_type: null,
        })
      )
    );
    vi.mocked(getAttendeeOptions).mockResolvedValue([]);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("handles no prefilled attendees", async () => {
    const userSession = {
      accessToken: "accessToken",
      email: "email",
      firstName: "firstName",
      lastName: "lastName",
      userId: uuidv4().toString(),
    };
    const userAttendeeOption = {
      uuid: userSession.userId,
      name: "User",
      role: "advisor",
      type: "user",
    };
    const attendeeOptions = AttendeeOptionsStruct.parse([
      {
        uuid: uuidv4().toString(),
        name: "John Doe",
        type: "client",
      },
      {
        uuid: uuidv4().toString(),
        name: "Jane Smith",
        role: "advisor",
        type: "user",
      },
      userAttendeeOption,
    ]);

    vi.mocked(getUserSessionOrRedirect).mockResolvedValue(userSession);
    vi.mocked(getAttendeeOptions).mockResolvedValue(attendeeOptions);

    const request = new Request("http://localhost:3000");
    const result = await loader({ request, params: {}, context: {} });

    expect(result.attendeeOptions).toEqual(
      attendeeOptions.sort((a, b) => a.name.localeCompare(b.name))
    );
    expect(result.initialAttendees).toEqual([userAttendeeOption]);
    expect(result.userAsAttendee).toEqual([userAttendeeOption]);
  });

  it("handles invalid attendees in query params gracefully", async () => {
    const userSession = {
      accessToken: "accessToken",
      email: "email",
      firstName: "firstName",
      lastName: "lastName",
      userId: uuidv4().toString(),
    };
    const userAttendeeOption = {
      uuid: userSession.userId,
      name: "User",
      role: "advisor",
      type: "user",
    };
    const attendeeOptions = AttendeeOptionsStruct.parse([
      {
        uuid: uuidv4().toString(),
        name: "John Doe",
        type: "client",
      },
      {
        uuid: uuidv4().toString(),
        name: "Jane Smith",
        role: "advisor",
        type: "user",
      },
      userAttendeeOption,
    ]);

    vi.mocked(getUserSessionOrRedirect).mockResolvedValue(userSession);
    vi.mocked(getAttendeeOptions).mockResolvedValue(attendeeOptions);

    const request = new Request("http://localhost:3000?attendees=invalid");
    const result = await loader({ request, params: {}, context: {} });

    expect(result.attendeeOptions).toEqual(
      attendeeOptions.sort((a, b) => a.name.localeCompare(b.name))
    );
    expect(result.initialAttendees).toEqual([userAttendeeOption]);
    expect(result.userAsAttendee).toEqual([userAttendeeOption]);
  });

  it("handles prefilled attendees", async () => {
    const userSession = {
      accessToken: "accessToken",
      email: "email",
      firstName: "firstName",
      lastName: "lastName",
      userId: uuidv4().toString(),
    };
    const userAttendeeOption = {
      uuid: userSession.userId,
      name: "User",
      role: "advisor",
      type: "user",
    };
    const attendeeOptions = AttendeeOptionsStruct.parse([
      {
        uuid: uuidv4().toString(),
        name: "John Doe",
        type: "client",
      },
      {
        uuid: uuidv4().toString(),
        name: "Jane Smith",
        role: "advisor",
        type: "user",
      },
      userAttendeeOption,
    ]);

    vi.mocked(getUserSessionOrRedirect).mockResolvedValue(userSession);
    vi.mocked(getAttendeeOptions).mockResolvedValue(attendeeOptions);

    const prefilledAttendees = [
      {
        uuid: uuidv4().toString(),
        name: "Prefilled One",
        type: "unknown",
      },
      {
        uuid: uuidv4().toString(),
        name: "Prefilled Two",
        type: "unknown",
      },
      {
        uuid: uuidv4().toString(),
        name: "Pokémon ⚡️",
        type: "unknown",
      },
    ];

    const prefilledAttendeesAsAttendeeOptions =
      AttendeeOptionsStruct.parse(prefilledAttendees);

    const request = new Request(
      `http://localhost:3000?attendees=${safeBtoa(
        JSON.stringify(prefilledAttendees)
      )}`
    );
    const result = await loader({ request, params: {}, context: {} });

    expect(result.attendeeOptions).toEqual(
      [...attendeeOptions, ...prefilledAttendeesAsAttendeeOptions].sort(
        (a, b) => a.name.localeCompare(b.name)
      )
    );
    expect(result.initialAttendees).toEqual([
      userAttendeeOption,
      ...prefilledAttendeesAsAttendeeOptions,
    ]);
  });

  it("matches prefilled attendees correctly", async () => {
    const userSession = {
      accessToken: "accessToken",
      email: "email",
      firstName: "firstName",
      lastName: "lastName",
      userId: uuidv4().toString(),
    };
    const attendeeUser = {
      uuid: userSession.userId,
      name: "User",
      role: "advisor",
      type: "user",
    };
    const client = {
      uuid: uuidv4().toString(),
      name: "John Doe",
      type: "client",
    };
    const user = {
      uuid: uuidv4().toString(),
      name: "Jane Smith",
      role: "advisor",
      type: "user",
    };
    const attendeeOptions = AttendeeOptionsStruct.parse([
      client,
      user,
      attendeeUser,
    ]);

    vi.mocked(getUserSessionOrRedirect).mockResolvedValue(userSession);
    vi.mocked(getAttendeeOptions).mockResolvedValue(attendeeOptions);

    const prefilledClient = {
      uuid: client.uuid,
      name: "Prefilled Client",
      type: "client",
    };
    const prefilledUnknownClient = {
      uuid: uuidv4().toString(),
      name: "Prefilled Unknown Client",
      type: "client",
    };
    const prefilledUser = {
      uuid: user.uuid,
      name: "Prefilled Two",
      type: "user",
    };
    const prefilledUnknownUser = {
      uuid: uuidv4().toString(),
      name: "Prefilled Unknown User",
      type: "user",
    };
    const prefilledUnknown = {
      uuid: uuidv4().toString(),
      name: "Prefilled Three",
      type: "unknown",
    };
    const prefilledAttendeeUser = {
      uuid: prefilledUser.uuid,
      name: "Prefilled Attendee User",
      type: "user",
    };
    const prefilledAttendees = [
      prefilledClient,
      prefilledUser,
      prefilledUnknown,
      prefilledUnknownClient,
      prefilledUnknownUser,
      prefilledAttendeeUser,
      prefilledUser,
      prefilledClient,
    ];

    const request = new Request(
      `http://localhost:3000?attendees=${safeBtoa(
        JSON.stringify(prefilledAttendees)
      )}`
    );
    const result = await loader({ request, params: {}, context: {} });

    expect(result.attendeeOptions).toEqual(
      [
        ...attendeeOptions,
        prefilledUnknown,
        prefilledUnknownClient,
        prefilledUnknownUser,
      ].sort((a, b) => a.name.localeCompare(b.name))
    );
    expect(result.initialAttendees).toEqual([
      attendeeUser,
      client,
      user,
      prefilledUnknown,
      prefilledUnknownClient,
      prefilledUnknownUser,
    ]);
  });

  it("handles other information from the search parameters", async () => {
    const userSession = {
      accessToken: "accessToken",
      email: "email",
      firstName: "firstName",
      lastName: "lastName",
      userId: uuidv4().toString(),
    };
    vi.mocked(getUserSessionOrRedirect).mockResolvedValue(userSession);

    const request = new Request(
      "http://localhost:3000?meetingTitle=Test&meetingLink=http://example.com&invalid=123&startTime=2023-08-01T12:00:00Z&endTime=2023-08-01T13:00:00Z&linkedCRMEntityID=123&linkedCRMEntityName=TestCRMEntity"
    );
    const result = await loader({ request, params: {}, context: {} });

    expect(result.initialMeetingTitle).toEqual("Test");
    expect(result.initialStartTime).toEqual("2023-08-01T12:00:00Z");
    expect(result.initialEndTime).toEqual("2023-08-01T13:00:00Z");
    expect(result.linkedCRMEntityID).toEqual("123");
    expect(result.linkedCRMEntityName).toEqual("TestCRMEntity");
    expect(result.initialMeetingLink).toEqual("http://example.com");
  });

  it("handles information from the note", async () => {
    const userSession = {
      accessToken: "accessToken",
      email: "email",
      firstName: "firstName",
      lastName: "lastName",
      userId: uuidv4().toString(),
    };

    // Set up the attendee options.
    const clientAttendeeOption = {
      uuid: uuidv4().toString(),
      name: "John Doe",
      type: "client",
    };
    const userAttendeeOption = {
      uuid: uuidv4().toString(),
      name: "Jane Smith",
      role: "advisor",
      type: "user",
    };
    const userAsAttendeeAttendeeOption = {
      uuid: userSession.userId,
      name: "User",
      role: "advisor",
      type: "user",
    };
    const attendeeOptions = AttendeeOptionsStruct.parse([
      clientAttendeeOption,
      userAttendeeOption,
      userAsAttendeeAttendeeOption,
    ]);

    // Set up the note with attendees, some matching attendee options and some not.
    const unknownNoteAttendeeUUID = uuidv4().toString();
    const note = {
      uuid: uuidv4(),
      created: new Date(),
      modified: new Date(),
      scheduledStartTime: new Date(),
      scheduledEndTime: new Date(),
      noteType: NoteType.MeetingRecording,
      status: ProcessingStatus.Uploaded,
      meetingName: "Test meeting",
      meetingDurationSeconds: 0,
      attendees: [
        {
          uuid: unknownNoteAttendeeUUID,
          name: "Note Attendee",
          type: AttendeeType.Unknown,
          speakerTime: null,
          speakerPercentage: null,
          clientUuid: null,
          userUuid: null,
        },
        {
          uuid: uuidv4().toString(),
          name: "John Doe From Note",
          type: AttendeeType.Client,
          speakerTime: null,
          speakerPercentage: null,
          clientUuid: clientAttendeeOption.uuid,
          userUuid: null,
        },
        {
          uuid: uuidv4().toString(),
          name: "Jane Smith From Note",
          type: AttendeeType.User,
          speakerTime: null,
          speakerPercentage: null,
          clientUuid: null,
          userUuid: userAttendeeOption.uuid,
        },
      ],
      tags: [],
      meetingTypeUuid: uuidv4().toString(),
      meetingCategory: "client",
      actionItems: [],
      advisorNotes: [],
      keyTakeaways: [],
      client: undefined,
      transcript: { utterances: [] },
      summaryByTopics: { sections: [] },
      isDeleted: false,
      botId: undefined,
      features: [],
      timesEditable: false,
      isPrivate: false,
      canUpdatePrivacyStatus: true,
    };

    vi.mocked(getUserSessionOrRedirect).mockResolvedValue(userSession);
    vi.mocked(getAttendeeOptions).mockResolvedValue(attendeeOptions);
    vi.mocked(getNoteById).mockResolvedValue(note);

    const request = new Request("http://localhost:3000?noteID=123");
    const result = await loader({ request, params: {}, context: {} });

    const unknownNoteAttendeeOption = {
      uuid: unknownNoteAttendeeUUID,
      name: "Note Attendee",
      type: "unknown",
    };
    expect(result.attendeeOptions).toEqual([
      userAttendeeOption,
      clientAttendeeOption,
      unknownNoteAttendeeOption,
      userAsAttendeeAttendeeOption,
    ]);
    expect(result.initialAttendees).toEqual([
      clientAttendeeOption,
      userAttendeeOption,
      unknownNoteAttendeeOption,
    ]);
    expect(result.userAsAttendee).toEqual([userAsAttendeeAttendeeOption]);
    expect(result.initialMeetingTitle).toEqual("Test meeting");
    expect(result.initialStartTime).toEqual(
      note.scheduledStartTime.toISOString()
    );
    expect(result.note).toEqual(note);
    expect(result.bot?.uuid).toBeFalsy();
    expect(result.eventSourceURL).toBeFalsy();
  });

  it("prioitizes information from the note or bot over query params", async () => {
    const bot = {
      meetingLink: "http://example.com/frombot",
      meetingType: BotMeetingType.VideoCall,
      platformId: null,
      status: BotStatus.InWaitingRoom,
      supportsPauseResume: true,
      uuid: uuidv4(),
    };
    const mockGetBot = vi.spyOn(BotApi.prototype, "botGetBot");
    mockGetBot.mockResolvedValue(bot);

    const userSession = {
      accessToken: "accessToken",
      email: "email",
      firstName: "firstName",
      lastName: "lastName",
      userId: uuidv4().toString(),
    };
    vi.mocked(getUserSessionOrRedirect).mockResolvedValue(userSession);

    const note = {
      uuid: uuidv4(),
      created: new Date(),
      modified: new Date(),
      scheduledStartTime: new Date(),
      scheduledEndTime: new Date(),
      noteType: NoteType.MeetingRecording,
      status: ProcessingStatus.Uploaded,
      meetingName: "Test meeting from note",
      meetingDurationSeconds: 0,
      attendees: [],
      tags: [],
      meetingTypeUuid: uuidv4().toString(),
      meetingCategory: "client",
      actionItems: [],
      advisorNotes: [],
      keyTakeaways: [],
      client: undefined,
      transcript: { utterances: [] },
      summaryByTopics: { sections: [] },
      isDeleted: false,
      botId: bot.uuid,
      features: [],
      timesEditable: false,
      isPrivate: false,
      canUpdatePrivacyStatus: true,
    };
    const mockGetNote = vi.mocked(getNoteById);
    mockGetNote.mockResolvedValue(note);

    const request = new Request(
      `http://localhost:3000?noteID=${note.uuid}&meetingTitle=Test&meetingLink=http://example.com&invalid=123&startTime=2023-08-01T12:00:00Z`
    );
    const result = await loader({ request, params: {}, context: {} });

    expect(result.initialAttendees).toEqual([]);
    expect(result.initialMeetingTitle).toEqual("Test meeting from note");
    expect(result.initialStartTime).toEqual(
      note.scheduledStartTime.toISOString()
    );
    expect(result.initialEndTime).toEqual(note.scheduledEndTime.toISOString());
    expect(result.initialMeetingLink).toEqual("http://example.com/frombot");
    expect(result.note).toEqual(note);
    expect(result.bot).toEqual(bot);
    expect(result.eventSourceURL).toBeFalsy();

    expect(mockGetNote).toHaveBeenCalledWith({ noteId: note.uuid, request });
    expect(mockGetBot).toHaveBeenCalledWith({ botUuid: bot.uuid });
  });

  it("handles API meeting types", async () => {
    const userSession = {
      accessToken: "accessToken",
      email: "email",
      firstName: "firstName",
      lastName: "lastName",
      userId: uuidv4().toString(),
    };

    vi.mocked(getUserSessionOrRedirect).mockResolvedValue(userSession);

    const meetingTypes = [
      {
        uuid: uuidv4().toString(),
        name: "Client",
        category: "client",
        is_shared: true,
      },
      {
        uuid: uuidv4().toString(),
        name: "Internal",
        category: "internal",
        is_shared: true,
      },
      {
        uuid: uuidv4().toString(),
        name: "Debrief",
        category: "debrief",
        is_shared: true,
      },
    ];

    mockFetch.mockResolvedValue({
      status: 200,
      json: async () =>
        Promise.resolve({
          meeting_types: meetingTypes,
          default_meeting_type: meetingTypes[0]!.uuid,
        }),
    });

    const request = new Request("http://localhost:3000");
    const result = await loader({ request, params: {}, context: {} });

    expect(result.meetingTypes).toEqual({
      meetingTypes: [
        {
          uuid: meetingTypes[0]!.uuid,
          name: meetingTypes[0]!.name,
          category: meetingTypes[0]!.category,
          isShared: meetingTypes[0]!.is_shared,
        },
        {
          uuid: meetingTypes[1]!.uuid,
          name: meetingTypes[1]!.name,
          category: meetingTypes[1]!.category,
          isShared: meetingTypes[1]!.is_shared,
        },
        {
          uuid: meetingTypes[2]!.uuid,
          name: meetingTypes[2]!.name,
          category: meetingTypes[2]!.category,
          isShared: meetingTypes[2]!.is_shared,
        },
      ],
      defaultMeetingType: meetingTypes[0]!.uuid,
    });
  });

  it("returns empty API meeting types", async () => {
    const userSession = {
      accessToken: "accessToken",
      email: "email",
      firstName: "firstName",
      lastName: "lastName",
      userId: uuidv4().toString(),
    };

    vi.mocked(getUserSessionOrRedirect).mockResolvedValue(userSession);

    mockFetch.mockResolvedValue({
      status: 200,
      json: async () =>
        Promise.resolve({
          meeting_types: [],
          default_meeting_type: null,
        }),
    });

    const request = new Request("http://localhost:3000");
    const result = await loader({ request, params: {}, context: {} });

    expect(result.meetingTypes).toEqual({
      meetingTypes: [],
      defaultMeetingType: null,
    });
  });
});

describe("action", () => {
  beforeEach(() => {
    vi.mocked(getUserSessionOrRedirect).mockResolvedValue({
      accessToken: "accessToken",
      email: "email",
      firstName: "firstName",
      lastName: "lastName",
      userId: uuidv4().toString(),
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("returns an error for unknown action types", async () => {
    const mockConsoleError = vi.spyOn(console, "error");
    mockConsoleError.mockImplementation(() => undefined);
    const formData = new FormData();
    formData.append("action", "testAction");
    const request = new Request("http://localhost:3000", {
      method: "POST",
      body: formData,
    });
    const result = await action({ request, params: {}, context: {} });

    if (result && "data" in result) {
      expect(result.data).toEqual({
        error: "Unknown action: testAction",
      });
    } else {
      throw new Error("Expected data response but got Response");
    }
    expect(mockConsoleError).toHaveBeenCalledWith(
      expect.anything(),
      "app/routes/notes.create.($id)/route.tsx action error",
      expect.stringContaining("Unknown action: testAction")
    );
  });

  it("returns an error if the bot ID is missing in the resume recording action", async () => {
    const mockConsoleError = vi.spyOn(console, "error");
    mockConsoleError.mockImplementation(() => undefined);
    const formData = new FormData();
    formData.append("action", ActionTypes.RESUME_RECORDING);
    const request = new Request("http://localhost:3000", {
      method: "POST",
      body: formData,
    });

    const result = await action({ request, params: {}, context: {} });

    if (result && "data" in result) {
      expect(result.data).toEqual({
        error: "Bot action requested without a bot ID",
      });
    } else {
      throw new Error("Expected data response but got Response");
    }
    expect(mockConsoleError).toHaveBeenCalledWith(
      expect.anything(),
      "app/routes/notes.create.($id)/route.tsx action error",
      expect.stringContaining("Bot action requested without a bot ID")
    );
  });

  it("handles resume recording action", async () => {
    const botId = uuidv4().toString();
    const formData = new FormData();
    formData.append("action", ActionTypes.RESUME_RECORDING);
    formData.append("botId", botId);
    const request = new Request("http://localhost:3000", {
      method: "POST",
      body: formData,
    });

    const botResumeRecording = vi.spyOn(BotApi.prototype, "botResumeRecording");
    botResumeRecording.mockReturnValue(Promise.resolve());

    await action({ request, params: {}, context: {} });

    expect(botResumeRecording).toHaveBeenCalledWith({ botUuid: botId });
  });

  it("returns an error if the bot ID is missing in the pause recording action", async () => {
    const mockConsoleError = vi.spyOn(console, "error");
    mockConsoleError.mockImplementation(() => undefined);
    const formData = new FormData();
    formData.append("action", ActionTypes.PAUSE_RECORDING);
    const request = new Request("http://localhost:3000", {
      method: "POST",
      body: formData,
    });
    const result = await action({ request, params: {}, context: {} });

    if (result && "data" in result) {
      expect(result.data).toEqual({
        error: "Bot action requested without a bot ID",
      });
    } else {
      throw new Error("Expected data response but got Response");
    }
    expect(mockConsoleError).toHaveBeenCalledWith(
      expect.anything(),
      "app/routes/notes.create.($id)/route.tsx action error",
      expect.stringContaining("Bot action requested without a bot ID")
    );
  });

  it("handles pause recording action", async () => {
    const botId = uuidv4().toString();
    const formData = new FormData();
    formData.append("action", ActionTypes.PAUSE_RECORDING);
    formData.append("botId", botId);
    const request = new Request("http://localhost:3000", {
      method: "POST",
      body: formData,
    });

    const botPauseRecording = vi.spyOn(BotApi.prototype, "botPauseRecording");
    botPauseRecording.mockReturnValue(Promise.resolve());

    await action({ request, params: {}, context: {} });

    expect(botPauseRecording).toHaveBeenCalledWith({ botUuid: botId });
  });

  it("handles save note action as a default action if there is no action specified", async () => {
    const noteId = uuidv4().toString();
    const attendeeId = uuidv4().toString();
    const formData = new FormData();
    formData.append(
      "attendees",
      JSON.stringify([{ name: "John Doe", type: "client", uuid: attendeeId }])
    );
    formData.append("meetingType", "client");
    formData.append("meetingName", "Test Meeting");
    const request = new Request("http://localhost:3000", {
      method: "POST",
      body: formData,
    });

    const noteCreateOrUpdateNote = vi.spyOn(
      NoteApi.prototype,
      "noteCreateOrUpdateNote"
    );
    noteCreateOrUpdateNote.mockResolvedValue({ noteId, completed: false });

    const result = await action({ request, params: {}, context: {} });

    expect(noteCreateOrUpdateNote).toHaveBeenCalledWith({
      noteId: null,
      meetingType: "client",
      meetingName: "Test Meeting",
      attendees: `[{"name":"John Doe","type":"client","uuid":"${attendeeId}"}]`,
      meetingLink: undefined,
      meetingSourceId: undefined,
      fileType: undefined,
      audioData: undefined,
    });
    expect(result).toEqual(
      redirect(`http://localhost:3000/notes/create/${noteId}?noteID=${noteId}`)
    );
  });

  it("handles save note action", async () => {
    const noteId = uuidv4().toString();
    const attendeeId = uuidv4().toString();
    const formData = new FormData();
    formData.append("action", ActionTypes.SAVE_NOTE);
    formData.append(
      "attendees",
      JSON.stringify([{ name: "John Doe", type: "client", uuid: attendeeId }])
    );
    formData.append("meetingType", "client");
    formData.append("meetingName", "Test Meeting");
    formData.append("meetingLink", "http://example.com");
    formData.append("meetingSourceID", "123");
    formData.append("startTime", "2023-08-01T12:00:00Z");
    formData.append("endTime", "2023-08-01T13:00:00Z");
    formData.append("fileContent", new Blob(["test audio data"]), "audio/wav");
    formData.append("fileType", "audio/wav");
    const request = new Request("http://localhost:3000", {
      method: "POST",
      body: formData,
    });

    const noteCreateOrUpdateNote = vi.spyOn(
      NoteApi.prototype,
      "noteCreateOrUpdateNote"
    );
    noteCreateOrUpdateNote.mockResolvedValue({ noteId, completed: false });

    const result = await action({ request, params: {}, context: {} });

    expect(noteCreateOrUpdateNote).toHaveBeenCalledWith({
      noteId: null,
      meetingType: "client",
      meetingName: "Test Meeting",
      attendees: `[{"name":"John Doe","type":"client","uuid":"${attendeeId}"}]`,
      meetingLink: "http://example.com",
      meetingSourceId: "123",
      scheduledStartTime: new Date("2023-08-01T12:00:00Z"),
      scheduledEndTime: new Date("2023-08-01T13:00:00Z"),
      fileType: "audio/wav",
      audioData: expect.any(File),
    });
    const callArgs = noteCreateOrUpdateNote.mock.calls[0]?.[0];
    const audioData = callArgs?.audioData;
    expect(await audioData?.text()).toEqual("test audio data");

    expect(result).toEqual(
      redirect(`http://localhost:3000/notes/create/${noteId}?noteID=${noteId}`)
    );
  });

  it("removes the calendar related URL parameters from the redirect URL", async () => {
    const noteId = uuidv4().toString();
    const attendeeId = uuidv4().toString();
    const formData = new FormData();
    formData.append("action", ActionTypes.SAVE_NOTE);
    formData.append(
      "attendees",
      JSON.stringify([{ name: "John Doe", type: "client", uuid: attendeeId }])
    );
    formData.append("meetingType", "client");
    formData.append("meetingName", "Test Meeting");
    const request = new Request(
      "http://localhost:3000/?meetingTitle=123&meetingLink=456&attendees=789&other=abc&startTime=123",
      {
        method: "POST",
        body: formData,
      }
    );

    const noteCreateOrUpdateNote = vi.spyOn(
      NoteApi.prototype,
      "noteCreateOrUpdateNote"
    );
    noteCreateOrUpdateNote.mockResolvedValue({ noteId, completed: false });

    const result = await action({ request, params: {}, context: {} });

    expect(noteCreateOrUpdateNote).toHaveBeenCalledWith({
      noteId: null,
      meetingType: "client",
      meetingName: "Test Meeting",
      attendees: `[{"name":"John Doe","type":"client","uuid":"${attendeeId}"}]`,
      meetingLink: undefined,
      meetingSourceId: undefined,
      fileType: undefined,
      audioData: undefined,
    });
    expect(result).toEqual(
      redirect(
        `http://localhost:3000/notes/create/${noteId}?other=abc&noteID=${noteId}`
      )
    );
  });

  it("handles save note action for an existing note", async () => {
    const noteId = uuidv4().toString();
    const attendeeId = uuidv4().toString();

    const formData = new FormData();
    formData.append("action", ActionTypes.SAVE_NOTE);
    formData.append(
      "attendees",
      JSON.stringify([{ name: "John Doe", type: "client", uuid: attendeeId }])
    );
    formData.append("meetingType", "client");
    formData.append("meetingName", "Test Meeting");
    const request = new Request(`http://localhost:3000/?noteID=${noteId}`, {
      method: "POST",
      body: formData,
    });

    const noteCreateOrUpdateNote = vi.spyOn(
      NoteApi.prototype,
      "noteCreateOrUpdateNote"
    );
    noteCreateOrUpdateNote.mockResolvedValue({ noteId, completed: false });

    const result = await action({ request, params: {}, context: {} });

    expect(noteCreateOrUpdateNote).toHaveBeenCalledWith({
      noteId,
      meetingType: "client",
      meetingName: "Test Meeting",
      attendees: `[{"name":"John Doe","type":"client","uuid":"${attendeeId}"}]`,
      meetingLink: undefined,
      meetingSourceId: undefined,
      fileType: undefined,
      audioData: undefined,
    });
    expect(result).toEqual(
      redirect(`http://localhost:3000/notes/create/${noteId}?noteID=${noteId}`)
    );
  });

  it("redirects correctly when a note is completed after saving", async () => {
    const noteId = uuidv4().toString();
    const attendeeId = uuidv4().toString();
    const formData = new FormData();
    formData.append("action", ActionTypes.SAVE_NOTE);
    formData.append(
      "attendees",
      JSON.stringify([{ name: "John Doe", type: "client", uuid: attendeeId }])
    );
    formData.append("meetingType", "client");
    formData.append("meetingName", "Test Meeting");
    const request = new Request("http://localhost:3000", {
      method: "POST",
      body: formData,
    });

    const noteCreateOrUpdateNote = vi.spyOn(
      NoteApi.prototype,
      "noteCreateOrUpdateNote"
    );
    noteCreateOrUpdateNote.mockResolvedValue({ noteId, completed: true });

    const result = await action({ request, params: {}, context: {} });

    expect(noteCreateOrUpdateNote).toHaveBeenCalledWith({
      noteId: null,
      meetingType: "client",
      meetingName: "Test Meeting",
      attendees: `[{"name":"John Doe","type":"client","uuid":"${attendeeId}"}]`,
      meetingLink: undefined,
      meetingSourceId: undefined,
      fileType: undefined,
      audioData: undefined,
    });
    expect(result).toEqual(redirect("/notes"));
  });

  it("handles delete note action", async () => {
    const noteId = uuidv4().toString();
    const formData = new FormData();
    formData.append("action", ActionTypes.DELETE_NOTE);
    const request = new Request(`http://localhost:3000/?noteID=${noteId}`, {
      method: "POST",
      body: formData,
    });

    const noteDeleteNote = vi.spyOn(NoteApi.prototype, "noteDeleteNote");
    noteDeleteNote.mockReturnValue(Promise.resolve());

    const result = await action({ request, params: {}, context: {} });

    expect(noteDeleteNote).toHaveBeenCalledWith({ noteId });
    expect(result).toEqual(redirect("/notes"));
  });

  it("fails the delete note action if there is no note ID", async () => {
    const mockConsoleError = vi.spyOn(console, "error");
    mockConsoleError.mockImplementation(() => undefined);

    const formData = new FormData();
    formData.append("action", ActionTypes.DELETE_NOTE);
    const request = new Request("http://localhost:3000", {
      method: "POST",
      body: formData,
    });

    const result = await action({ request, params: {}, context: {} });

    // Check if result has data property (DataWithResponseInit) vs being a Response
    if (result && "data" in result) {
      expect(result.data).toEqual({
        error: "Delete note action requested without a note ID",
      });
    } else {
      throw new Error("Expected data response but got Response");
    }
    expect(mockConsoleError).toHaveBeenCalledWith(
      expect.anything(),
      "app/routes/notes.create.($id)/route.tsx action error",
      expect.stringContaining("Delete note action requested without a note ID")
    );
  });
});
