// TODO: @debojyotighosh Implement a common component that replicates the behaviour of this / DropdownMenu.

import React, { useRef, useState } from "react";
import { useOnClickOutside } from "usehooks-ts";

import { Button } from "~/@shadcn/ui/button";

const ActionButtonDrawer = ({ children }: { children: React.ReactNode }) => {
  const [showActionButtonDrawer, setShowActionButtonDrawer] = useState(false);
  const ref = useRef(null);

  useOnClickOutside(ref, () => {
    // NOTE: This setTimeout hack is used to avoid reopening the drawer when the trigger is clicked; needs testing
    setTimeout(() => {
      setShowActionButtonDrawer(false);
    }, 0);
  });

  return (
    <div className="relative">
      <Button
        variant="outline"
        onClick={() => setShowActionButtonDrawer(!showActionButtonDrawer)}
      >
        …
      </Button>

      {showActionButtonDrawer && (
        <div
          ref={ref}
          className="absolute right-0 top-full mt-1 flex flex-col items-start gap-1 rounded-sm border bg-card p-1 shadow-lg"
        >
          {children}
        </div>
      )}
    </div>
  );
};

export default ActionButtonDrawer;
