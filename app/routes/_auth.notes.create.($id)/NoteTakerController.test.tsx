import { render, screen } from "@testing-library/react";
import {
  NoteTakerController,
  NoteTakerControllerProps,
} from "./NoteTakerController";
import { BotMeetingType, BotStatus } from "~/api/openapi/generated";
import { vi } from "vitest";
import { createMemoryRouter, RouterProvider } from "react-router";
import userEvent from "@testing-library/user-event";

// Mock the Remix hooks
const mockSubmit = vi.fn();
vi.mock("react-router", async () => {
  const actual = await vi.importActual("react-router");
  return {
    ...actual,
    useSubmit: () => mockSubmit,
    Form: ({ children, ...props }: any) => <form {...props}>{children}</form>,
  };
});

describe("NoteTakerController", () => {
  const defaultProps = {
    botId: "test-bot-id",
    botStatus: BotStatus.Unknown,
    meetingLink: "",
    setMeetingLink: vi.fn(),
    meetingLinkSuggestions: [],
    onTouched: vi.fn(),
    needConsent: false,
    sendBotAndCreateNote: vi.fn(),
    saveNoteTaking: vi.fn(),
    spinnerCTA: "",
    setSpinnerCTA: vi.fn(),
    supportsPauseResume: true,
    botType: BotMeetingType.VideoCall,
    botOperationLoading: false,
  };

  beforeEach(() => {
    mockSubmit.mockClear();
  });

  // Renders the notetaker controller in a router.
  const renderInRouter = (props: NoteTakerControllerProps) => {
    const router = createMemoryRouter(
      [
        {
          path: "/",
          element: <NoteTakerController {...props} />,
          action: async () => ({ success: true }), // Mock action for form submissions
        },
      ],
      { initialEntries: ["/"] }
    );
    render(<RouterProvider router={router} />);
  };

  it("displays the correct messages for video call bot type", () => {
    renderInRouter(defaultProps);

    expect(
      screen.getByPlaceholderText("Paste video meeting link...")
    ).toBeInTheDocument();
    expect(screen.queryByText("Consent Received")).not.toBeInTheDocument();
    expect(screen.getByText("Send Notetaker")).toBeInTheDocument();
    expect(screen.queryByRole("textbox")).not.toHaveAttribute("type", "tel");
  });

  it("displays the correct messages for phone call bot type", () => {
    renderInRouter({ ...defaultProps, botType: BotMeetingType.PhoneCall });

    expect(screen.queryByText("Consent Received")).not.toBeInTheDocument();
    expect(
      screen.getByPlaceholderText("Enter phone number...")
    ).toBeInTheDocument();
    expect(screen.getByText("Call")).toBeInTheDocument();
    expect(screen.getByRole("textbox")).toHaveAttribute("type", "tel");
  });

  it("shows the bot UI when the consent checkbox is checked", async () => {
    renderInRouter({ ...defaultProps, needConsent: true });
    const user = userEvent.setup();
    await user.click(screen.getByRole("checkbox"));

    expect(
      screen.getByPlaceholderText("Paste video meeting link...")
    ).toBeInTheDocument();
    expect(screen.getByText("Send Notetaker")).not.toBeHidden();
    expect(screen.getByText("Consent Received")).not.toBeHidden();
    expect(screen.getByRole("checkbox")).toBeChecked();
  });

  it("handles link suggestions", async () => {
    renderInRouter({
      ...defaultProps,
      meetingLinkSuggestions: [
        { link: "test-link", name: "Test Name" },
        { link: "test-link-2", name: "Test Name 2" },
      ],
    });
    const user = userEvent.setup();
    const textbox = screen.getByRole("textbox");
    await user.click(textbox);
    expect(screen.getByText("test-link (Test Name)")).toBeInTheDocument();
    expect(screen.getByText("test-link-2 (Test Name 2)")).toBeInTheDocument();
  });

  it("filters link suggestions", async () => {
    renderInRouter({
      ...defaultProps,
      meetingLinkSuggestions: [
        { link: "test-link", name: "Test Name" },
        { link: "test-link-2", name: "Test Name 2" },
      ],
      meetingLink: "Test Name 2",
    });
    const user = userEvent.setup();
    const textbox = screen.getByRole("textbox");
    await user.click(textbox);
    expect(screen.queryByText("test-link (Test Name)")).not.toBeInTheDocument();
    expect(screen.getByText("test-link-2 (Test Name 2)")).toBeInTheDocument();
  });

  it("calls setMeetingLink when input changes", async () => {
    renderInRouter(defaultProps);
    const user = userEvent.setup();
    const input = screen.getByPlaceholderText("Paste video meeting link...");
    await user.type(input, "n");
    expect(defaultProps.setMeetingLink).toHaveBeenCalledWith("n");
  });

  it("calls sendBotAndCreateNote when send button is clicked", async () => {
    const user = userEvent.setup();
    renderInRouter({ ...defaultProps, meetingLink: "test-link" });
    await user.click(screen.getByRole("button"));
    expect(defaultProps.sendBotAndCreateNote).toHaveBeenCalled();
  });

  it("disables input and button when bot is in call", () => {
    renderInRouter({
      ...defaultProps,
      botStatus: BotStatus.InCallNotRecording,
    });
    const input = screen.getByPlaceholderText("Paste video meeting link...");
    const leaveButton = screen.getByText("Leave Meeting");
    const sendButton = screen.getByText("Notetaker sent");
    expect(input).toBeDisabled();
    expect(leaveButton).toBeEnabled();
    expect(sendButton).toBeDisabled();
  });

  it("hides the pause button when bot does not support pause/resume", () => {
    renderInRouter({
      ...defaultProps,
      supportsPauseResume: false,
      botStatus: BotStatus.InCallRecording,
    });
    const pauseButton = screen.queryByText("Pause");
    expect(pauseButton).not.toBeInTheDocument();
  });

  it("calls startRecording when start recording button is clicked", async () => {
    const user = userEvent.setup();
    renderInRouter({
      ...defaultProps,
      botStatus: BotStatus.InCallNotRecording,
    });
    const startButton = screen.getByText("Start Notetaking");
    const stopButton = screen.queryByText("Stop Notetaking");
    expect(stopButton).not.toBeInTheDocument();
    await user.click(startButton);
    expect(defaultProps.setSpinnerCTA).toHaveBeenCalledWith(
      "Starting Notetaking"
    );
    const formData = new FormData();
    formData.set("action", "resume_recording");
    formData.set("botId", defaultProps.botId);
    expect(mockSubmit).toHaveBeenCalledWith(formData, {
      method: "post",
      encType: "multipart/form-data",
    });
  });

  it("calls stopRecording when start recording button is clicked", async () => {
    const user = userEvent.setup();
    renderInRouter({ ...defaultProps, botStatus: BotStatus.InCallRecording });
    const startButton = screen.queryByText("Start Notetaking");
    const stopButton = screen.getByText("Stop Notetaking");
    expect(startButton).not.toBeInTheDocument();
    await user.click(stopButton);
    expect(defaultProps.setSpinnerCTA).toHaveBeenCalledWith(
      "Stopping Notetaking"
    );
    const formData = new FormData();
    formData.set("action", "pause_recording");
    formData.set("botId", defaultProps.botId);
    expect(mockSubmit).toHaveBeenCalledWith(formData, {
      method: "post",
      encType: "multipart/form-data",
    });
  });

  it("calls saveNoteTaking when finish button is clicked", async () => {
    const user = userEvent.setup();
    renderInRouter({ ...defaultProps, botStatus: BotStatus.InCallRecording });
    const endCallButton = screen.getByText("Leave Meeting");
    await user.click(endCallButton);
    expect(defaultProps.saveNoteTaking).toHaveBeenCalled();
  });

  it("displays the correct messages for phone call bot type when in call not recording", () => {
    renderInRouter({
      ...defaultProps,
      botType: BotMeetingType.PhoneCall,
      botStatus: BotStatus.InCallNotRecording,
    });

    expect(screen.queryByText("Consent Received")).not.toBeInTheDocument();
    expect(
      screen.getByPlaceholderText("Enter phone number...")
    ).toBeInTheDocument();
    expect(screen.getByText("Call in progress")).toBeInTheDocument();
    expect(screen.getByText("Start Notetaking")).toBeInTheDocument();
    expect(screen.queryByText("Stop Notetaking")).not.toBeInTheDocument();
  });

  it("displays the correct messages for phone call bot type when in call recording", () => {
    renderInRouter({
      ...defaultProps,
      botType: BotMeetingType.PhoneCall,
      botStatus: BotStatus.InCallRecording,
    });

    expect(screen.queryByText("Consent Received")).not.toBeInTheDocument();
    expect(
      screen.getByPlaceholderText("Enter phone number...")
    ).toBeInTheDocument();
    expect(screen.getByText("Call in progress")).toBeInTheDocument();
    expect(screen.queryByText("Start Notetaking")).not.toBeInTheDocument();
    expect(screen.getByText("Stop Notetaking")).toBeInTheDocument();
  });
});
