import { useState, type ReactNode } from "react";
import { z } from "zod";
import { ToggleGroup, ToggleGroupItem } from "~/@shadcn/ui/toggle-group";
import { cn } from "~/@shadcn/utils";

// Types
const TabStruct = z.enum(["client-recap", "notes", "holdings", "transactions"]);
type Tab = z.infer<typeof TabStruct>;

// Fragments
type TabContentsProps = {
  activeTab: string;
  children: ReactNode;
  value: string;
};
const TabContents = ({ activeTab, children, value }: TabContentsProps) => (
  <div className={cn("flex flex-col gap-2", value !== activeTab && "hidden")}>
    {children}
  </div>
);

// Exports
export const ClientTabGroup = ({
  notesTab,
  clientRecapTab,
  holdingsTab,
  transactionsTab,
}: {
  notesTab: ReactNode;
  clientRecapTab: ReactNode;
  holdingsTab?: ReactNode;
  transactionsTab?: ReactNode;
}) => {
  const [activeTab, setActiveTab] = useState<Tab>("client-recap");

  return (
    <>
      <ToggleGroup
        className="justify-start p-0"
        value={activeTab}
        type="single"
        onValueChange={(value) => {
          const nextTab = TabStruct.safeParse(value);
          if (nextTab.success) setActiveTab(nextTab.data);
        }}
      >
        <ToggleGroupItem value="client-recap" aria-label="Show client recap">
          Client Recap
        </ToggleGroupItem>
        <ToggleGroupItem value="notes" aria-label="Show notes">
          Notes
        </ToggleGroupItem>
        {holdingsTab && (
          <ToggleGroupItem value="holdings" aria-label="Show holdings">
            Holdings
          </ToggleGroupItem>
        )}
        {transactionsTab && (
          <ToggleGroupItem value="transactions" aria-label="Show transactions">
            Transactions
          </ToggleGroupItem>
        )}
      </ToggleGroup>

      <TabContents activeTab={activeTab} value="client-recap">
        {clientRecapTab}
      </TabContents>
      <TabContents activeTab={activeTab} value="notes">
        {notesTab}
      </TabContents>
      {holdingsTab && (
        <TabContents activeTab={activeTab} value="holdings">
          {holdingsTab}
        </TabContents>
      )}
      {transactionsTab && (
        <TabContents activeTab={activeTab} value="transactions">
          {transactionsTab}
        </TabContents>
      )}
    </>
  );
};
