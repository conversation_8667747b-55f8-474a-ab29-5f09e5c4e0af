import { useState, useEffect } from "react";
import { Spark<PERSON> } from "lucide-react";
import { useFetcher } from "react-router";

import { But<PERSON> } from "~/@shadcn/ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import { ModifyNoteWithPromptModal } from "~/@ui/ModifyNoteModal";
import { NoteResponse } from "~/api/openapi/generated";

const ModifyNoteWithPromptSection = ({
  noteId,
  onNoteUpdate,
}: {
  noteId: string;
  onNoteUpdate: (note: NoteResponse) => void;
}) => {
  const [isModifyWithPromptModalOpen, setIsModifyWithPromptModalOpen] =
    useState(false);
  const [modifyWithPromptError, setModifyWithPromptError] = useState<
    string | null
  >(null);
  const modifyWithPromptFetcher = useFetcher();

  useEffect(() => {
    if (modifyWithPromptFetcher.data) {
      if (modifyWithPromptFetcher.data.success) {
        setIsModifyWithPromptModalOpen(false);
        setModifyWithPromptError(null);

        if (modifyWithPromptFetcher.data.note) {
          onNoteUpdate(modifyWithPromptFetcher.data.note);
        }
      } else {
        setModifyWithPromptError(
          modifyWithPromptFetcher.data.errors?.[0] ||
            "Failed to modify note with prompt."
        );
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [modifyWithPromptFetcher.data]);
  // onNoteUpdate changes at every reload and we don't want this effect to re-run then if the note has not actually updated

  return (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="outline_magic"
            onClick={() => setIsModifyWithPromptModalOpen(true)}
            data-onboarding="modify-note-with-prompt"
          >
            <Sparkles className="!h-5 !w-5" />{" "}
            <span className="bg-magic hidden bg-clip-text text-transparent sm:block">
              Modify Note with Prompt
            </span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>Modify this note with an AI prompt</TooltipContent>
      </Tooltip>

      <ModifyNoteWithPromptModal
        open={isModifyWithPromptModalOpen}
        onOpen={() => setModifyWithPromptError(null)}
        onClose={() => {
          setIsModifyWithPromptModalOpen(false);
          setModifyWithPromptError(null);
        }}
        onSubmit={(prompt) => {
          setModifyWithPromptError(null);
          if (!noteId) {
            setModifyWithPromptError("Note ID not available");
            return;
          }
          const formData = new FormData();
          formData.append("actionType", "llm-update-with-prompt");
          formData.append("prompt", prompt);
          modifyWithPromptFetcher.submit(formData, {
            method: "post",
            action: `/notes/${noteId}`,
            encType: "multipart/form-data",
          });
        }}
        loading={
          modifyWithPromptFetcher.state === "submitting" ||
          modifyWithPromptFetcher.state === "loading"
        }
        error={modifyWithPromptError}
        clearOnSuccess={modifyWithPromptFetcher.data?.success}
      />
    </>
  );
};

export default ModifyNoteWithPromptSection;
