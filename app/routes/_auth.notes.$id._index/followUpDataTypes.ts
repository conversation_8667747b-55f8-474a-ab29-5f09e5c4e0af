import { z } from "zod";
import {
  FollowUp,
  FollowUpStatus,
  NoteResponse,
} from "~/api/openapi/generated";
export const BaseStructuredReviewDataEntry = z.object({
  id: z.string(),
  kind: z.enum(["header", "toggle", "select", "multiselect", "bullet"]),
  topic: z.string(),
  discussed: z.optional(z.boolean()),
  options: z.optional(z.array(z.string())),
  selected: z.optional(z.string()),
  multi_selected: z.optional(z.array(z.string())),
  explanation: z.string().optional(),
  evidence: z
    .array(
      z.object({
        quote: z.string(),
        timestamp: z.string().optional(),
      })
    )
    .optional(),
});

export type StructuredReviewDataEntry = z.infer<
  typeof BaseStructuredReviewDataEntry
> & { subentries?: StructuredReviewDataEntry[] };

export const StructuredReviewDataEntry: z.ZodType<StructuredReviewDataEntry> =
  BaseStructuredReviewDataEntry.extend({
    subentries: z.lazy(() => StructuredReviewDataEntry.array().optional()),
  });

export const StructuredReviewDataStruct = z.object({
  review_entries: z.array(StructuredReviewDataEntry),
});
export type StructuredReviewData = z.infer<typeof StructuredReviewDataStruct>;

export const NumericalDataStruct = z.object({
  table_definition: z.object({
    title: z.string().optional(),
    description: z.string().optional(),
    columns: z.array(
      z.object({
        id: z.string(),
        caption: z.string(),
        data_type: z.string(),
      })
    ),
  }),
  data: z.array(
    z.object({
      values: z.record(z.any()),
      format: z.object({
        decimal_places: z.number().optional(),
        show_symbol: z.boolean().optional(),
        date_format: z.string().optional(),
        value_type: z.enum(["number", "percentage", "currency", "date"]),
      }),
      evidence: z
        .array(
          z.object({
            quote: z.string(),
            timestamp: z.string(),
          })
        )
        .optional(),
    })
  ),
});
export type NumericalData = z.infer<typeof NumericalDataStruct>;

export const UnstructuredTextDataStruct = z.object({
  content: z.string(),
  format: z.enum(["markdown"]),
});
export type UnstructuredTextData = z.infer<typeof UnstructuredTextDataStruct>;

export type ParsedFollowUpData =
  | { followUp: FollowUp; parsedData: StructuredReviewData }
  | { followUp: FollowUp; parsedData: NumericalData }
  | { followUp: FollowUp; parsedData: UnstructuredTextData }
  | { followUp: FollowUp; parsedData: {} };

export const structuredFollowUpDataSchemaID =
  "https://zeplyn.ai/structured_review_data.schema.json";
export const numericalFollowUpDataSchemaID =
  "https://zeplyn.ai/tabular_numerical_data.schema.json";
export const unstructuredTextFollowUpDataSchemaID =
  "https://zeplyn.ai/unstructured_text_content.schema.json";

export const followUpTypes: Record<string, any> = {
  [numericalFollowUpDataSchemaID]: NumericalDataStruct,
  [structuredFollowUpDataSchemaID]: StructuredReviewDataStruct,
  [unstructuredTextFollowUpDataSchemaID]: UnstructuredTextDataStruct,
};

// Helpers

export const followUpDataForFollowUp = (
  followUp?: FollowUp | null
): ParsedFollowUpData | undefined => {
  if (!followUp) {
    return undefined;
  }

  if (!followUp.schema || !("$id" in followUp.schema)) {
    return undefined;
  }

  const structType = followUpTypes[followUp.schema.$id as string];
  if (!structType) {
    return undefined;
  }

  if (followUp.status !== FollowUpStatus.Completed) {
    return { followUp, parsedData: {} };
  }

  const result = structType.safeParse(followUp.data);
  if (!result.success) {
    return undefined;
  }

  return { followUp, parsedData: result.data };
};

export const followUpDataForNote = (
  note: NoteResponse
): ParsedFollowUpData[] => {
  return (note.followUps ?? [])
    .map(followUpDataForFollowUp)
    .filter((f): f is ParsedFollowUpData => !!f);
};

export const copyableTextForData = (data: StructuredReviewData) => {
  const generateText = (
    entries: StructuredReviewDataEntry[],
    depth: number
  ) => {
    let text = "";
    entries.forEach((entry) => {
      text += `${"    ".repeat(depth)}• ${entry.topic}\n`;
      if (entry.subentries) {
        text += generateText(entry.subentries, depth + 1);
      }
    });
    return text;
  };
  return generateText(data.review_entries ?? [], 0);
};
