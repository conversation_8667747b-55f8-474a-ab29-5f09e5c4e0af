import { FileDown } from "lucide-react";
import { But<PERSON> } from "~/@shadcn/ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import { Link } from "react-router";

type ExportPdfButtonProps = {
  noteId: string;
};

export const ExportPdfButton = ({ noteId }: ExportPdfButtonProps) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button variant="ghost" size="icon-sm" asChild>
          <Link to={`/feapi/notes/${noteId}/export`} reloadDocument>
            <FileDown />
          </Link>
        </Button>
      </TooltipTrigger>
      <TooltipContent side="bottom">Export as PDF</TooltipContent>
    </Tooltip>
  );
};
