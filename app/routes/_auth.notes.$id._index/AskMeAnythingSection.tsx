import { useCallback } from "react";
import { Sparkles } from "lucide-react";

import { SummarySection } from "~/api/openapi/generated";
import AskAnythingModal, { AnswerStatus } from "~/@ui/AskAnythingModal";
import { Button } from "~/@shadcn/ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";

import { copyFormattedVersionToClipboard } from "~/utils/copyToClipboard";

const AskMeAnythingSection = ({
  answer,
  answerStatus,
  onSubmit,
  query,
  setQuery,
  isModalOpen,
  setIsModalOpen,
  showAskInput,
  addOrReplaceSummaryTopic,
  showReplaceCta,
}: {
  answer: SummarySection | null;
  answerStatus: AnswerStatus;
  onSubmit: () => void;
  query: string;
  setQuery: (query: string) => void;
  isModalOpen: boolean;
  setIsModalOpen: (isModalOpen: boolean) => void;
  showAskInput: () => void;
  addOrReplaceSummaryTopic: (shouldReplace: boolean) => void;
  showReplaceCta: boolean;
}) => {
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      if (event.key === "Enter" && query.trim()) {
        event.preventDefault();
        onSubmit();
      }
    },
    [onSubmit, query]
  );

  const handleCopy = () => {
    if (answer) {
      copyFormattedVersionToClipboard(
        [
          {
            text: answer.topic,
            renderTextAsHeader: true,
            list: answer.bullets,
          },
        ],
        "answer"
      );
    }
  };

  return (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="outline_magic"
            onClick={() => setIsModalOpen(true)}
            data-onboarding="ask-anything"
          >
            <Sparkles className="!h-5 !w-5" />{" "}
            <span className="bg-magic hidden bg-clip-text text-transparent sm:block">
              Ask Anything
            </span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>Ask anything about this note</TooltipContent>
      </Tooltip>

      {isModalOpen && (
        <AskAnythingModal
          closeModal={() => setIsModalOpen(false)}
          answerStatus={answerStatus}
          query={query}
          answer={answer}
          handleKeyDown={handleKeyDown}
          onInputChange={(e) => {
            setQuery(e.target.value);
          }}
          handleAsk={onSubmit}
          addSummaryTopic={() => addOrReplaceSummaryTopic(false)}
          replaceSummaryTopic={() => addOrReplaceSummaryTopic(true)}
          setQuery={setQuery}
          showAskInput={showAskInput}
          showReplaceCta={showReplaceCta}
          handleCopy={handleCopy}
        />
      )}
    </>
  );
};

export default AskMeAnythingSection;
