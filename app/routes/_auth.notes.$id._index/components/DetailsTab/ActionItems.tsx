import { Dispatch, useEffect, useState } from "react";
import ReactMarkdown from "react-markdown";
import { ArrowDownUp, ArrowUpRight, Copy, Delete, Plus } from "lucide-react";

import ActionItemV2 from "./ActionItemV2";
import {
  EditableNoteActions,
  EditableNoteState,
} from "../../editableNoteReducer";
import { copyFormattedVersionToClipboard } from "~/utils/copyToClipboard";
import { Typography } from "~/@ui/Typography";
import shouldShowCopyButton from "../../utils/shouldShowCopyButton";
import { Checkbox } from "~/@shadcn/ui/checkbox";
import { TextareaGrowable } from "~/@shadcn/ui/textarea";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import { Button } from "~/@shadcn/ui/button";
import { ProcessingStatus, TaskResponse } from "~/api/openapi/generated";
import { useFlag } from "~/context/flags";
import NewActionItem from "./NewActionItem";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/@shadcn/ui/select";

// Fragments
type EditableActionItemsProps = {
  dispatch: Dispatch<EditableNoteActions>;
  actionItems: EditableNoteState["actionItems"];
  disabled: boolean;
  onEdit: () => void;
  isVisible: boolean;
  parentNoteUuid: string;
  tasks: TaskResponse[];
  noteStatus: ProcessingStatus;
  userUuid: string;
};

const ActionItems = ({
  dispatch,
  actionItems,
  disabled,
  onEdit,
  isVisible,
  parentNoteUuid,
  tasks: tasksProp,
  noteStatus,
  userUuid,
}: EditableActionItemsProps) => {
  const [itemsState, setItemsState] = useState(
    actionItems.map((item) => ({
      ...item,
      value: item.value,
    }))
  );
  const [tasks, setTasks] = useState(tasksProp);

  const [sortBy, setSortBy] = useState(sortByFilters[0]?.value);
  const [showAddTaskInput, setShowAddTaskInput] = useState(false);
  const isTaskUiRevampEnabled = useFlag("EnableNewActionItemsInterface");

  useEffect(() => {
    setItemsState(
      actionItems.map((item) => ({
        ...item,
        value: item.value,
      }))
    );
  }, [actionItems]);

  useEffect(() => {
    setTasks(tasksProp);
  }, [tasksProp]);

  const handleAddItem = () => {
    if (disabled) return;
    dispatch({ type: "addActionItem" });
  };

  const handleCopySection = () => {
    copyFormattedVersionToClipboard(
      [
        {
          text: "Action items",
          renderTextAsHeader: true,
          list: itemsState.map((item) => item.value),
        },
      ],
      "Action items"
    );
  };

  const handleItemChange = (uuid: string, value: string) => {
    const updatedItems = itemsState.map((item) =>
      item.uuid === uuid ? { ...item, value } : item
    );

    setItemsState(updatedItems);

    dispatch({
      type: "updateActionItem",
      uuid: uuid,
      nextValue: value,
    });

    onEdit();
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === "Enter" && !disabled) {
      event.preventDefault();
      handleAddItem();
    }
  };

  // add task in local state to solve revalidation lag; used in ActionItemsV2 only
  const onTaskCreation = (task: TaskResponse) => {
    setTasks((prev) => prev.concat(task));
  };

  // remove task from local state; used in ActionItemsV2 only
  const deleteTask = (uuid: string) => {
    setTasks(tasks.filter((task) => task.uuid !== uuid));
  };

  return (
    <>
      <div className="flex flex-wrap items-center gap-1">
        <Typography variant="h3" color="primary">
          Action items
        </Typography>
        {shouldShowCopyButton(actionItems) && (
          <Copy
            className="ml-2 cursor-pointer text-gray-500 hover:text-black"
            onClick={handleCopySection}
          />
        )}
        {((!disabled && !isTaskUiRevampEnabled) || isTaskUiRevampEnabled) && (
          <Plus
            className="ml-2 cursor-pointer text-gray-500 hover:text-black"
            onClick={() => {
              if (isTaskUiRevampEnabled) {
                setShowAddTaskInput(true);
              } else {
                handleAddItem();
              }
            }}
          />
        )}

        {/* sort */}
        {isTaskUiRevampEnabled && (
          <div className="flex basis-full items-center gap-1 sm:ml-auto sm:basis-0">
            <ArrowDownUp size={16} />
            <span className="flex items-center text-sm font-semibold">
              Sort:
            </span>

            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="ml-1 w-fit px-2 py-1 text-xs sm:ml-2">
                <SelectValue placeholder="Sort" />
              </SelectTrigger>
              <SelectContent>
                {sortByFilters.map((sort) => (
                  <SelectItem key={sort.value} value={sort.value}>
                    {sort.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
      </div>

      {showAddTaskInput && (
        <NewActionItem
          parentNoteUuid={parentNoteUuid}
          userUuid={userUuid}
          onDone={(task?: TaskResponse) => {
            if (task) {
              onTaskCreation(task);
            }

            setShowAddTaskInput(false);
          }}
        />
      )}

      {isTaskUiRevampEnabled &&
        getSortedTasks(tasks, sortBy).map(
          (item: TaskResponse, index: number) => (
            <ActionItemV2
              key={item.uuid}
              parentNoteUuid={parentNoteUuid}
              data={item}
              disabled={noteStatus === ProcessingStatus.Finalized}
              onDelete={deleteTask}
            />
          )
        )}

      {!isTaskUiRevampEnabled && (
        <ul className="flex list-disc flex-col gap-2 pl-4">
          {itemsState
            .filter(({ action }) => action !== "delete")
            .map(({ uuid, value, autoFocus, checked }, index) => (
              <li
                key={uuid}
                className="-ml-2 flex items-center gap-1 whitespace-pre-wrap text-warning"
              >
                <Checkbox
                  defaultChecked={checked}
                  onCheckedChange={(checked) => {
                    if (!disabled && typeof checked === "boolean") {
                      dispatch({ type: "setActionItemChecked", uuid, checked });
                      onEdit();
                    }
                  }}
                  disabled={disabled}
                />
                {disabled ? (
                  <Typography
                    className="flex-grow overflow-hidden break-words text-lg"
                    asChild
                  >
                    <ReactMarkdown>{value}</ReactMarkdown>
                  </Typography>
                ) : (
                  <>
                    <TextareaGrowable
                      autoFocus={autoFocus}
                      value={value}
                      placeholder="Add an action item"
                      className="flex-grow border-none text-lg !opacity-100 focus:outline-none"
                      onChange={(event) => {
                        if (disabled) {
                          return;
                        }
                        handleItemChange(uuid, event.currentTarget.value);
                      }}
                      onBlur={(event) => {
                        if (
                          !disabled &&
                          event.currentTarget.value.trim().length === 0
                        ) {
                          dispatch({ type: "removeActionItem", uuid });
                          onEdit();
                        }
                      }}
                      onKeyDown={handleKeyDown}
                      disabled={disabled}
                      isVisible={isVisible}
                    />
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant={"ghost"}
                          onClick={() => {
                            dispatch({
                              type: "removeActionItem",
                              uuid,
                            });
                            onEdit();
                          }}
                        >
                          <Delete />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>Remove this action item</TooltipContent>
                    </Tooltip>
                  </>
                )}
                <ArrowUpRight
                  className="ml-2 cursor-pointer text-gray-500 hover:text-black"
                  onClick={() => (window.location.href = `/tasks/${uuid}`)}
                />
              </li>
            ))}
        </ul>
      )}
    </>
  );
};

const sortByFilters = [
  {
    label: "Assignee (A-Z)",
    value: "ASSIGNEE_ASC",
  },
  {
    label: "Assignee (Z-A)",
    value: "ASSIGNEE_DSC",
  },
  {
    label: "Due date (ascending)",
    value: "DUE_DATE_ASC",
  },
  {
    label: "Due date (descending)",
    value: "DUE_DATE_DSC",
  },
];

function getSortedTasks(tasks: TaskResponse[], sortBy?: string) {
  return tasks.sort((a, b) => {
    switch (sortBy) {
      case "ASSIGNEE_DSC":
        return b.assignee?.name?.localeCompare(a.assignee?.name || "") || 0;
      case "DUE_DATE_ASC":
        if (a.dueDate && b.dueDate) {
          return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
        }
        return a.dueDate ? -1 : 1;
      case "DUE_DATE_DSC":
        if (a.dueDate && b.dueDate) {
          return new Date(b.dueDate).getTime() - new Date(a.dueDate).getTime();
        }
        return a.dueDate ? -1 : 1;
      case "ASSIGNEE_ASC":
      default:
        return a.assignee?.name?.localeCompare(b.assignee?.name || "") || 0;
    }
  });
}

export default ActionItems;
