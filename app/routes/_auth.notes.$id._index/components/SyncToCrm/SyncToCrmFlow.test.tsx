import { describe, it, expect, vi, beforeEach } from "vitest";
import { toast } from "react-toastify";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { type ClientSelectionModalProps } from "./ClientSelectionModal";

import {
  ApiRoutersCrmClientResponse,
  ClientType,
  NoteResponse,
} from "~/api/openapi/generated";
import { fetchPost } from "~/utils/fetch";

import SyncToCrmFlow from "./SyncToCrmFlow";
import { SyncDataSelectionModalProps } from "./SyncDataSelectionModal";

// Mock external dependencies
vi.mock("react-toastify", () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn(),
  },
}));

vi.mock("~/utils/fetch", () => ({
  fetchPost: vi.fn(),
}));

const mockRevalidate = vi.fn();

vi.mock("react-router", async (importOriginal) => {
  const actual = await importOriginal<typeof import("react-router")>();
  return {
    ...actual,
    useRevalidator: () => ({ revalidate: mockRevalidate }),
  };
});

// Mock child components
vi.mock("./SyncDataSelectionModal", () => ({
  default: ({
    dataSet,
    selectionEnabled,
    onClose,
    onContinue,
    isSaving,
    title,
  }: SyncDataSelectionModalProps) => (
    <div data-testid="sync-data-selection-modal">
      <p>Data Selection Modal</p>
      <p>Is saving: {isSaving.toString()}</p>
      <button onClick={() => onContinue({ meeting_details: {} })}>
        Continue
      </button>

      <p>Sync selection enabled: {selectionEnabled ? "Yes" : "No"}</p>
      {title && <p>{title}</p>}

      {dataSet && <p>Has data set</p>}
      {(dataSet ?? []).map((d) => (
        <div key={d.key}>
          <span>{d.name}</span>
          <span>{d.selectionContent}</span>
        </div>
      ))}
      <button onClick={onClose}>Close</button>
    </div>
  ),
}));

vi.mock("./ClientSelectionModal", () => ({
  default: ({
    onClose,
    onContinue,
    isSaving,
    currentClient,
  }: ClientSelectionModalProps) => (
    <div data-testid="client-selection-modal">
      <p>Client Selection Modal</p>
      <p>Is saving: {isSaving.toString()}</p>
      <p>Current client: {currentClient?.uuid ?? "none selected"}</p>
      <button
        onClick={() =>
          onContinue(
            currentClient ?? {
              uuid: "client-1",
              name: "John Doe",
              type: "individual",
            }
          )
        }
      >
        Select Client
      </button>
      <button onClick={onClose}>Close</button>
    </div>
  ),
}));

describe("SyncToCrmFlow", () => {
  const mockDispatch = vi.fn();

  const mockClients: ApiRoutersCrmClientResponse[] = [
    { uuid: "client-1", name: "John Doe", type: ClientType.Individual },
    { uuid: "client-2", name: "Jane Smith", type: ClientType.Household },
  ];

  const mockCurrentClient: ApiRoutersCrmClientResponse = {
    name: "John Doe",
    uuid: "client-1",
    type: "individual",
  };

  const mockNoteData: NoteResponse = {
    uuid: "note-123",
    meetingName: "Test Meeting",
    status: "processed",
    noteType: "meeting_recording",
    created: new Date("2023-04-01T10:00:00.000Z"),
    modified: new Date("2023-04-01T10:00:00.000Z"),
    meetingDurationSeconds: 3600,
    meetingTypeUuid: null,
    meetingCategory: "client",
    actionItems: [],
    advisorNotes: [],
    keyTakeaways: [],
    client: undefined,
    transcript: { utterances: [] },
    summaryByTopics: { sections: [] },
    isDeleted: false,
    botId: undefined,
    features: [],
    timesEditable: false,
    isPrivate: false,
    canUpdatePrivacyStatus: true,
  };

  const defaultProps = {
    clients: mockClients,
    currentClient: mockCurrentClient,
    dispatch: mockDispatch,
    data: mockNoteData,
    noteId: "note-123",
    disabled: false,
    syncSelectionEnabled: true,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(toast.error).mockClear();
    vi.mocked(toast.success).mockClear();

    // Reset fetch mock
    global.fetch = vi.fn();

    // Reset fetchPost mock
    vi.mocked(fetchPost).mockReset();
  });

  it("renders the sync to CRM button", () => {
    render(<SyncToCrmFlow {...defaultProps} />);

    const button = screen.getByRole("button", { name: "Sync to CRM" });
    expect(button).toBeInTheDocument();
    expect(button).toHaveAttribute("data-onboarding", "sync-crm-cta");
  });

  it("disables the button when disabled prop is true", () => {
    render(<SyncToCrmFlow {...defaultProps} disabled={true} />);

    const button = screen.getByRole("button", { name: "Sync to CRM" });
    expect(button).toBeDisabled();
  });

  it("opens client selection modal when there is no current client and clients exist", async () => {
    const user = userEvent.setup();
    render(<SyncToCrmFlow {...defaultProps} currentClient={undefined} />);

    const button = screen.getByRole("button", { name: "Sync to CRM" });
    await user.click(button);

    expect(screen.getByTestId("client-selection-modal")).toBeInTheDocument();
    expect(screen.getByText("Client Selection Modal")).toBeInTheDocument();
    expect(
      screen.getByText("Current client: none selected")
    ).toBeInTheDocument();
  });

  it("opens client selection modal when the current client exists", async () => {
    const user = userEvent.setup();
    render(<SyncToCrmFlow {...defaultProps} />);

    const button = screen.getByRole("button", { name: "Sync to CRM" });
    await user.click(button);

    expect(screen.getByTestId("client-selection-modal")).toBeInTheDocument();
    expect(screen.getByText("Client Selection Modal")).toBeInTheDocument();
    expect(
      screen.getByText(`Current client: ${mockCurrentClient.uuid}`)
    ).toBeInTheDocument();
  });

  it("opens sync selection modal when the current client exists but there is no way to update the client", async () => {
    const user = userEvent.setup();
    render(<SyncToCrmFlow {...defaultProps} dispatch={undefined} />);

    const button = screen.getByRole("button", { name: "Sync to CRM" });
    await user.click(button);

    expect(screen.getByTestId("sync-data-selection-modal")).toBeInTheDocument();
    expect(screen.getByText("Data Selection Modal")).toBeInTheDocument();
  });

  it("fetches note data when data is not provided", async () => {
    const user = userEvent.setup();
    const mockFetch = vi.fn().mockResolvedValue({
      json: () => Promise.resolve(mockNoteData),
    });
    global.fetch = mockFetch;

    render(<SyncToCrmFlow {...defaultProps} data={undefined} />);

    const button = screen.getByRole("button", { name: "Sync to CRM" });
    await user.click(button);

    expect(mockFetch).toHaveBeenCalledWith("/feapi/notes/id?noteId=note-123");
  });

  it("does not fetch note data when data is not provided but sync selection is disabled", async () => {
    const user = userEvent.setup();
    const mockFetch = vi.fn().mockResolvedValue({
      json: () => Promise.resolve(mockNoteData),
    });
    global.fetch = mockFetch;

    render(
      <SyncToCrmFlow
        {...defaultProps}
        data={undefined}
        syncSelectionEnabled={false}
      />
    );

    const button = screen.getByRole("button", { name: "Sync to CRM" });
    await user.click(button);

    expect(mockFetch).not.toHaveBeenCalledWith(
      "/feapi/notes/id?noteId=note-123"
    );
  });

  it("uses provided note data when noteId exists but data is provided", async () => {
    const user = userEvent.setup();
    const mockFetch = vi.fn().mockResolvedValue({
      json: () => Promise.resolve(mockNoteData),
    });
    global.fetch = mockFetch;

    render(<SyncToCrmFlow {...defaultProps} data={mockNoteData} />);

    const button = screen.getByRole("button", { name: "Sync to CRM" });
    await user.click(button);

    expect(mockFetch).not.toHaveBeenCalled();
  });

  it("handles client selection and saves client", async () => {
    const user = userEvent.setup();
    vi.mocked(fetchPost).mockResolvedValue({ success: true });

    render(<SyncToCrmFlow {...defaultProps} currentClient={undefined} />);

    // Open client selection modal
    const button = screen.getByRole("button", { name: "Sync to CRM" });
    await user.click(button);

    // Select a client
    const selectClientButton = screen.getByText("Select Client");
    await user.click(selectClientButton);

    await waitFor(() => {
      expect(fetchPost).toHaveBeenCalledWith("/feapi/notes/save", {
        noteId: "note-123",
        clientName: "John Doe",
        clientId: "client-1",
      });
    });

    expect(mockDispatch).toHaveBeenCalledWith({
      type: "updateClient",
      client: mockClients[0],
    });
  });

  it("handles sync data selection and syncs to CRM successfully", async () => {
    const user = userEvent.setup();
    vi.mocked(fetchPost).mockResolvedValue({ success: true });

    render(<SyncToCrmFlow {...defaultProps} />);

    // Open and click through client selection modal
    await user.click(screen.getByRole("button", { name: "Sync to CRM" }));
    await user.click(screen.getByRole("button", { name: "Select Client" }));

    vi.mocked(fetchPost).mockResolvedValue({
      success: true,
      userInputRequired: false,
    });

    // Continue with data selection
    expect(screen.getByText("Attributes to sync")).toBeInTheDocument();
    expect(screen.getByText("Has data set")).toBeInTheDocument();

    const continueButton = screen.getByText("Continue");
    await user.click(continueButton);

    await waitFor(() => {
      expect(fetchPost).toHaveBeenCalledWith("/feapi/notes/sync-to-crm", {
        noteId: "note-123",
        uploadTargetId: "",
        syncItems: { meeting_details: {} },
      });
    });

    await waitFor(() => {
      expect(vi.mocked(toast.success)).toHaveBeenCalledWith(
        "Note synced to CRM",
        expect.any(Object)
      );
    });

    // Modal should be closed
    expect(
      screen.queryByTestId("sync-data-selection-modal")
    ).not.toBeInTheDocument();
  });

  it("handles syncs to CRM successfully when sync data selection is disabled", async () => {
    const user = userEvent.setup();
    vi.mocked(fetchPost).mockResolvedValue({ success: true });

    render(<SyncToCrmFlow {...defaultProps} syncSelectionEnabled={false} />);

    // Open and click through client selection modal
    await user.click(screen.getByRole("button", { name: "Sync to CRM" }));
    await user.click(screen.getByRole("button", { name: "Select Client" }));

    vi.mocked(fetchPost).mockResolvedValue({
      success: true,
      userInputRequired: false,
    });

    // Continue with data selection
    const continueButton = screen.getByText("Continue");
    expect(
      screen.getByText("Sync to CRM", { selector: "p" })
    ).toBeInTheDocument();
    expect(
      screen.queryByText(
        "Select the data you want to sync to your CRM. Once this note is synced, you will not be able to edit it on Zeplyn."
      )
    ).not.toBeInTheDocument();
    await user.click(continueButton);

    await waitFor(() => {
      expect(fetchPost).toHaveBeenCalledWith("/feapi/notes/sync-to-crm", {
        noteId: "note-123",
        uploadTargetId: "",
        // This is hardcoded in the mock component above.
        syncItems: { meeting_details: {} },
      });
    });

    await waitFor(() => {
      expect(vi.mocked(toast.success)).toHaveBeenCalledWith(
        "Note synced to CRM",
        expect.any(Object)
      );
    });

    // Modal should be closed
    expect(
      screen.queryByTestId("sync-data-selection-modal")
    ).not.toBeInTheDocument();
  });

  it("uses the latest passed-in note data when it changes", async () => {
    const user = userEvent.setup();
    vi.mocked(fetchPost).mockResolvedValue({ success: true });

    const { rerender } = render(<SyncToCrmFlow {...defaultProps} />);

    // Open and click through client selection modal
    await user.click(screen.getByRole("button", { name: "Sync to CRM" }));
    await user.click(screen.getByRole("button", { name: "Select Client" }));

    vi.mocked(fetchPost).mockResolvedValue({
      success: true,
      userInputRequired: false,
    });

    expect(screen.getByText("Meeting title")).toBeInTheDocument();
    expect(screen.getByText("Test Meeting")).toBeInTheDocument();

    const updatedData = {
      ...defaultProps,
      data: { ...mockNoteData, meetingName: "Updated meeting title" },
    };

    rerender(<SyncToCrmFlow {...updatedData} />);

    await user.click(screen.getByRole("button", { name: "Sync to CRM" }));
    await user.click(screen.getByRole("button", { name: "Select Client" }));

    expect(screen.getByText("Meeting title")).toBeInTheDocument();
    expect(screen.getByText("Updated meeting title")).toBeInTheDocument();
    expect(screen.queryByText("Test Meeting")).not.toBeInTheDocument();
  });

  it("handles sync to CRM error", async () => {
    const user = userEvent.setup();
    vi.mocked(fetchPost).mockResolvedValue({ success: true });

    render(<SyncToCrmFlow {...defaultProps} />);

    // Open and click through client selection modal
    await user.click(screen.getByRole("button", { name: "Sync to CRM" }));
    await user.click(screen.getByRole("button", { name: "Select Client" }));

    vi.mocked(fetchPost).mockResolvedValue({
      success: false,
      error: "Invalid credentials",
    });

    const continueButton = screen.getByText("Continue");
    await user.click(continueButton);

    await waitFor(() => {
      expect(vi.mocked(toast.error)).toHaveBeenCalledWith(
        "Failed to sync note to CRM. Invalid credentials",
        expect.any(Object)
      );
    });
  });

  it("handles unexpected server response", async () => {
    const user = userEvent.setup();
    vi.mocked(fetchPost).mockResolvedValue({ success: true });

    render(<SyncToCrmFlow {...defaultProps} />);

    // Open and click through client selection modal
    await user.click(screen.getByRole("button", { name: "Sync to CRM" }));
    await user.click(screen.getByRole("button", { name: "Select Client" }));

    vi.mocked(fetchPost).mockResolvedValue({
      success: true,
      userInputRequired: true,
      uploadTargetOptions: undefined, // This should cause the error
    });

    const continueButton = screen.getByText("Continue");
    await user.click(continueButton);

    await waitFor(() => {
      expect(vi.mocked(toast.error)).toHaveBeenCalledWith(
        "Failed to sync note to CRM. Please try again",
        expect.any(Object)
      );
    });
  });

  it("handles network error during sync", async () => {
    const user = userEvent.setup();
    vi.mocked(fetchPost).mockResolvedValue({ success: true });

    render(<SyncToCrmFlow {...defaultProps} />);

    // Open and click through client selection modal
    await user.click(screen.getByRole("button", { name: "Sync to CRM" }));
    await user.click(screen.getByRole("button", { name: "Select Client" }));

    vi.mocked(fetchPost).mockRejectedValue(new Error("Network error"));

    const continueButton = screen.getByText("Continue");
    await user.click(continueButton);

    await waitFor(() => {
      expect(vi.mocked(toast.error)).toHaveBeenCalledWith(
        "Failed to sync note to CRM. Please try again",
        expect.any(Object)
      );
    });
  });

  it("closes all modals when closeAll is called", async () => {
    const user = userEvent.setup();
    render(<SyncToCrmFlow {...defaultProps} />);

    // Open data selection modal
    const button = screen.getByRole("button", { name: "Sync to CRM" });
    await user.click(button);

    expect(screen.getByTestId("client-selection-modal")).toBeInTheDocument();

    // Close modal
    const closeButton = screen.getByText("Close");
    await user.click(closeButton);

    expect(
      screen.queryByTestId("client-selection-modal")
    ).not.toBeInTheDocument();
  });

  it("closes all modals when closeAll is called on the sync modal", async () => {
    const user = userEvent.setup();
    render(<SyncToCrmFlow {...defaultProps} dispatch={undefined} />);

    // Open data selection modal
    const button = screen.getByRole("button", { name: "Sync to CRM" });
    await user.click(button);

    expect(screen.getByTestId("sync-data-selection-modal")).toBeInTheDocument();

    // Close modal
    const closeButton = screen.getByText("Close");
    await user.click(closeButton);

    expect(
      screen.queryByTestId("sync-data-selection-modal")
    ).not.toBeInTheDocument();
  });

  it("shows loading state during saving", async () => {
    const user = userEvent.setup();

    vi.mocked(fetchPost).mockResolvedValue({ success: true });

    render(<SyncToCrmFlow {...defaultProps} />);

    // Open and click through client selection modal
    await user.click(screen.getByRole("button", { name: "Sync to CRM" }));
    await user.click(screen.getByRole("button", { name: "Select Client" }));

    // Mock a delayed response
    vi.mocked(fetchPost).mockImplementation(
      () =>
        new Promise((resolve) => {
          setTimeout(
            () => resolve({ success: true, userInputRequired: false }),
            5000
          );
        })
    );

    // The modal should show saving state
    expect(screen.getByText("Is saving: false")).toBeInTheDocument();

    // Continue (this will start the async operation)
    const continueButton = screen.getByText("Continue");
    await user.click(continueButton);
  });

  it("shows toast error when no client is selected and no dispatch function is provided", async () => {
    const user = userEvent.setup();
    render(
      <SyncToCrmFlow
        {...defaultProps}
        currentClient={undefined}
        dispatch={undefined}
      />
    );

    const button = screen.getByRole("button", { name: "Sync to CRM" });
    await user.click(button);

    expect(toast.error).toHaveBeenCalledWith(
      "Please select a client before syncing to CRM",
      expect.objectContaining({
        autoClose: 2000,
        toastId: "no-client-selected-error",
      })
    );
  });
});
