import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { Calendar, FileText, Users } from "lucide-react";
import SyncDataSelectionModal from "./SyncDataSelectionModal";

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe("SyncDataSelectionModal", () => {
  const mockOnClose = vi.fn();
  const mockOnContinue = vi.fn();

  const mockDataSet = [
    {
      Icon: Calendar,
      name: "Meeting Details",
      key: "meeting_details",
      selectionContent: "Meeting name, date, and duration information",
    },
    {
      Icon: FileText,
      name: "Summary",
      key: "summary",
      selectionContent: "AI-generated meeting summary and key points",
    },
    {
      Icon: Users,
      name: "Attendees",
      key: "attendees",
      selectionContent: "List of meeting participants",
      isReadOnly: true,
    },
  ];

  const defaultProps = {
    selectionEnabled: true,
    dataSet: mockDataSet,
    onClose: mockOnClose,
    onContinue: mockOnContinue,
    isSaving: false,
    title: "Title",
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock the fetch response for CRM certification text
    const mockPreferencesResponse = {
      orgPreferences: [
        {
          title: "CRM Certification",
          jsonSchema: {
            properties: {
              advisor_certification_display_text: {
                default:
                  "I certify that the summary of notes has been reviewed and verified, and is accurate to the best of my knowledge.",
              },
            },
          },
        },
      ],
    };

    mockFetch.mockResolvedValue({
      json: () => Promise.resolve(mockPreferencesResponse),
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("renders the modal with correct title and description when sync data is loaded", async () => {
    render(<SyncDataSelectionModal {...defaultProps} />);

    expect(screen.getByRole("dialog")).toBeInTheDocument();
    expect(screen.getByText("Title")).toBeInTheDocument();
    expect(
      screen.getByText(
        "Select the data you want to sync to your CRM. Once this note is synced, you will not be able to edit it on Zeplyn."
      )
    ).toBeInTheDocument();
    expect(screen.getByTestId("loading-skeleton")).toBeInTheDocument();
    expect(
      screen.queryByText((content) =>
        content.includes("you certify that the interaction record")
      )
    ).not.toBeInTheDocument();
    expect(screen.getByText("Synchronize")).toBeDisabled();
  });

  it("shows the correct description when dataSet is not provided", async () => {
    render(<SyncDataSelectionModal {...defaultProps} dataSet={undefined} />);

    expect(
      screen.getByText("Please wait while we load the details for you.")
    ).toBeInTheDocument();
    expect(screen.getByTestId("loading-skeleton")).toBeInTheDocument();
    expect(
      screen.queryByText((content) =>
        content.includes("you certify that the interaction record")
      )
    ).not.toBeInTheDocument();
    expect(screen.getByText("Synchronize")).toBeDisabled();
  });

  it("renders all data items with correct names and icons", async () => {
    render(<SyncDataSelectionModal {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText("Meeting Details")).toBeInTheDocument();
      expect(screen.getByText("Summary")).toBeInTheDocument();
      expect(screen.getByText("Attendees")).toBeInTheDocument();
    });
  });

  it("renders no data items or description when selection is not enabled", async () => {
    render(
      <SyncDataSelectionModal {...defaultProps} selectionEnabled={false} />
    );

    await waitFor(() => {
      expect(screen.queryByText("Meeting Details")).not.toBeInTheDocument();
      expect(screen.queryByText("Summary")).not.toBeInTheDocument();
      expect(screen.queryByText("Attendees")).not.toBeInTheDocument();

      expect(
        screen.queryByText(
          "Select the data you want to sync to your CRM. Once this note is synced, you will not be able to edit it on Zeplyn."
        )
      ).not.toBeInTheDocument();
      expect(
        screen.queryByText("Please wait while we load the details for you.")
      ).not.toBeInTheDocument();
    });
  });

  it("initializes all items as selected by default", async () => {
    render(<SyncDataSelectionModal {...defaultProps} />);

    await waitFor(() => {
      const switches = screen.getAllByRole("switch");

      // Meeting Details should be checked (not readonly)
      expect(switches[0]).toBeChecked();

      // Summary should be checked (not readonly)
      expect(switches[1]).toBeChecked();

      // Attendees should be checked (readonly, always checked)
      expect(switches[2]).toBeChecked();
    });
  });

  it("allows toggling non-readonly items", async () => {
    const user = userEvent.setup();
    render(<SyncDataSelectionModal {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText("Meeting Details")).toBeInTheDocument();
    });

    const switches = screen.getAllByRole("switch");
    const meetingDetailsSwitch = switches[0]!;

    // Initially checked
    expect(meetingDetailsSwitch).toBeChecked();

    // Toggle off
    await user.click(meetingDetailsSwitch);
    expect(meetingDetailsSwitch).not.toBeChecked();

    // Toggle back on
    await user.click(meetingDetailsSwitch);
    expect(meetingDetailsSwitch).toBeChecked();
  });

  it("prevents toggling readonly items", async () => {
    const user = userEvent.setup();
    render(<SyncDataSelectionModal {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText("Attendees")).toBeInTheDocument();
    });

    const switches = screen.getAllByRole("switch");
    const attendeesSwitch = switches[2]!; // Attendees is the readonly item

    expect(attendeesSwitch).toBeDisabled();
    expect(attendeesSwitch).toBeChecked();

    // Attempt to click (should not change state)
    await user.click(attendeesSwitch);
    expect(attendeesSwitch).toBeChecked();
  });

  it("expands and collapses attribute cells when clicked", async () => {
    const user = userEvent.setup();
    render(<SyncDataSelectionModal {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText("Meeting Details")).toBeInTheDocument();
    });

    // Initially collapsed, content should not be visible
    expect(
      screen.queryByText("Meeting name, date, and duration information")
    ).not.toBeInTheDocument();

    // Click on the cell to expand
    await user.click(screen.getByText("Meeting Details").closest("div")!);

    expect(
      screen.getByText("Meeting name, date, and duration information")
    ).toBeInTheDocument();

    // Click again to collapse
    await user.click(screen.getByText("Meeting Details").closest("div")!);

    expect(
      screen.queryByText("Meeting name, date, and duration information")
    ).not.toBeInTheDocument();
  });

  it("prevents cell collapse/expand when clicking on switch", async () => {
    const user = userEvent.setup();
    render(<SyncDataSelectionModal {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText("Meeting Details")).toBeInTheDocument();
    });

    const switches = screen.getAllByRole("switch");
    const meetingDetailsSwitch = switches[0]!;

    // Content should not be visible initially
    expect(
      screen.queryByText("Meeting name, date, and duration information")
    ).not.toBeInTheDocument();

    // Click on switch (should not expand the cell)
    await user.click(meetingDetailsSwitch);

    expect(
      screen.queryByText("Meeting name, date, and duration information")
    ).not.toBeInTheDocument();
  });

  it("disables Synchronize button when no non-readonly sections are selected", async () => {
    const user = userEvent.setup();
    render(<SyncDataSelectionModal {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText("Meeting Details")).toBeInTheDocument();
    });

    const synchronizeButton = screen.getByRole("button", {
      name: "Synchronize",
    });
    const switches = screen.getAllByRole("switch");

    // Initially enabled (non-readonly items are selected)
    expect(synchronizeButton).toBeEnabled();

    // Uncheck all non-readonly items
    await user.click(switches[0]!); // Meeting Details
    await user.click(switches[1]!); // Summary

    // Should now be disabled
    expect(synchronizeButton).toBeDisabled();
  });

  it("calls onClose when Cancel button is clicked", async () => {
    const user = userEvent.setup();
    render(<SyncDataSelectionModal {...defaultProps} />);

    const cancelButton = screen.getByRole("button", { name: "Cancel" });
    await user.click(cancelButton);

    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it("calls onContinue with correct selection data when Synchronize is clicked", async () => {
    const user = userEvent.setup();
    render(<SyncDataSelectionModal {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText("Meeting Details")).toBeInTheDocument();
    });

    const synchronizeButton = screen.getByRole("button", {
      name: "Synchronize",
    });
    await user.click(synchronizeButton);

    expect(mockOnContinue).toHaveBeenCalledTimes(1);
    expect(mockOnContinue).toHaveBeenCalledWith({
      meeting_details: {
        includeSection: true,
        includedItems: [],
      },
      summary: {
        includeSection: true,
        includedItems: [],
      },
      // Note: attendees is readonly and should not be included in selection data
    });
  });

  it("shows saving state when isSaving is true", () => {
    render(<SyncDataSelectionModal {...defaultProps} isSaving={true} />);

    const synchronizeButton = screen.getByRole("button", {
      name: "Synchronizing...",
    });
    expect(synchronizeButton).toBeDisabled();
    expect(synchronizeButton).toHaveTextContent("Synchronizing...");
  });

  it("shows certification message when loaded", async () => {
    render(<SyncDataSelectionModal {...defaultProps} />);

    expect(screen.getByTestId("loading-skeleton")).toBeInTheDocument();

    // Wait for the component to be fully loaded
    await waitFor(() => {
      expect(screen.getByText("Meeting Details")).toBeInTheDocument();
    });

    // First verify the footer buttons are rendered
    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: "Synchronize" })
    ).toBeInTheDocument();

    // Then check for certification text
    await waitFor(() => {
      expect(screen.queryByTestId("loading-skeleton")).not.toBeInTheDocument();
      expect(
        screen.getByText(
          (_, element) =>
            element?.textContent ===
            "By clicking Synchronize, I certify that the summary of notes has been reviewed and verified, and is accurate to the best of my knowledge."
        )
      ).toBeInTheDocument();
      expect(
        screen.getByRole("button", {
          name: "Synchronize",
        })
      ).toBeEnabled();
    });
  });

  it("shows certification message when loaded without selection enabled", async () => {
    render(
      <SyncDataSelectionModal {...defaultProps} selectionEnabled={false} />
    );

    // First verify the footer buttons are rendered correctly
    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    const syncButton = screen.getByRole("button", { name: "Synchronize" });
    expect(syncButton).toBeInTheDocument();
    expect(syncButton).toBeDisabled();

    // Then check for certification text
    await waitFor(() => {
      expect(
        screen.getByText(
          (_, element) =>
            element?.textContent ===
            "By clicking Synchronize, I certify that the summary of notes has been reviewed and verified, and is accurate to the best of my knowledge."
        )
      ).toBeInTheDocument();
      expect(
        screen.getByRole("button", {
          name: "Synchronize",
        })
      ).toBeEnabled();
    });
  });

  it("shows tooltip for readonly items", async () => {
    render(<SyncDataSelectionModal {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText("Attendees")).toBeInTheDocument();
    });

    const switches = screen.getAllByRole("switch");
    const attendeesSwitch = switches[2];

    expect(attendeesSwitch).toHaveAttribute(
      "title",
      "This attribute will always be synced"
    );
  });

  it("applies correct CSS classes for selected and readonly items", async () => {
    render(<SyncDataSelectionModal {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText("Meeting Details")).toBeInTheDocument();
    });

    // Check that selected items have success border - need to get the parent div of the text
    const meetingDetailsText = screen.getByText("Meeting Details");
    const meetingDetailsCell = meetingDetailsText.closest(
      "div[class*='border']"
    );
    expect(meetingDetailsCell).toHaveClass("border-success");

    // Check that readonly items have success border
    const attendeesText = screen.getByText("Attendees");
    const attendeesCell = attendeesText.closest("div[class*='border']");
    expect(attendeesCell).toHaveClass("border-success");
  });
});
