import { useMemo, useState } from "react";
import { useRevalidator } from "react-router";
import { RefreshCcw } from "lucide-react";
import { toast } from "react-toastify";

import { Button } from "~/@shadcn/ui/button";
import {
  ApiRoutersCrmClientResponse,
  CRMSyncItemSelection,
  CRMUploadTarget,
  NoteResponse,
} from "~/api/openapi/generated";
import SyncDataSelectionModal from "./SyncDataSelectionModal";

import { getCardData } from "./utils";
import ClientSelectionModal from "./ClientSelectionModal";
import { EditableNoteActions } from "~/routes/_auth.notes.$id._index/editableNoteReducer";
import TargetSelectionModal from "./TargetSelectionModal";
import { fetchPost } from "~/utils/fetch";
import { useFlag } from "~/context/flags";

enum Modal {
  SelectionAndCertification = "selection-and-certification",
  ClientSelection = "client-selection",
  TargetSelection = "target-selection",
}

type SyncToCrmFlowProps = {
  currentClient?: ApiRoutersCrmClientResponse;
  syncSelectionEnabled?: boolean;
  dispatch?: React.Dispatch<EditableNoteActions>;
  data?: NoteResponse;
  noteId: string;
  disabled?: boolean;
};
const SyncToCrmFlow = ({
  currentClient,
  syncSelectionEnabled,
  dispatch,
  data,
  noteId,
  disabled,
}: SyncToCrmFlowProps) => {
  const [fetchedNoteData, setFetchedNoteData] = useState<
    NoteResponse | undefined
  >(undefined);
  const [selectedModal, setSelectedModal] = useState<string | null>();
  const [selections, setSelections] = useState<
    Record<string, CRMSyncItemSelection> | undefined
  >(undefined);
  const [syncTargets, setSyncTargets] = useState<CRMUploadTarget[]>([]);
  const [displayTitle, setDisplayTitle] = useState<string | undefined>();
  const [displayDescription, setDisplayDescription] = useState<
    string | undefined
  >();

  const [isSaving, setIsSaving] = useState(false);

  const enableNoteTabsNameChanges = useFlag("EnableNoteDetailsPageNameChanges");

  const revalidator = useRevalidator();

  async function loadNoteData() {
    const noteData = await fetch(`/feapi/notes/id?noteId=${noteId}`).then(
      (res) => res.json()
    );

    setFetchedNoteData(noteData);
  }

  // when the S2C CTA is clicked
  const onClickCta = () => {
    // load the note data if it hasn't been provided or previously fetched
    // and we are in sync selection mode
    if (!data && !fetchedNoteData && syncSelectionEnabled) {
      // make API call using noteId
      loadNoteData();
    }

    // Make sure we either have a client or have a way to update the client.
    if (!dispatch && !currentClient) {
      toast.error("Please select a client before syncing to CRM", {
        autoClose: 2000,
        toastId: "no-client-selected-error",
      });
      return;
    }

    // If we don't have a dispatch function, we can't update the client, so we don't show the client
    // selection modal. The previous check ensures that we have a currentClient in this case.
    if (!dispatch) {
      setSelectedModal(Modal.SelectionAndCertification);
    } else {
      setSelectedModal(Modal.ClientSelection);
    }
  };

  const noteData = data || fetchedNoteData;

  const closeAll = () => {
    setSelectedModal(null);
  };

  const saveClient = async (selectedClient: ApiRoutersCrmClientResponse) => {
    setIsSaving(true);

    if (!dispatch) {
      return;
    }

    try {
      dispatch({
        type: "updateClient",
        client: selectedClient,
      });

      const { success } = await fetchPost("/feapi/notes/save", {
        noteId: noteId,
        clientName: selectedClient.name,
        clientId: selectedClient.uuid,
      });

      if (success) {
        setSelectedModal(Modal.SelectionAndCertification); // trigger the next phase
      } else {
        throw new Error("Failed to save client");
      }
    } catch (e) {
      // Revert the client selection in case of failure.
      dispatch({
        type: "updateClient",
        client: undefined,
      });
      toast.error("Failed to save client. Please try again.", {
        autoClose: 2000,
        toastId: "save-client-error",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const saveToCrmFromAttributeSelection = (
    syncItems: Record<string, CRMSyncItemSelection> | undefined
  ) => {
    // save `syncItems` in case upload targets flow is invoked
    setSelections(syncItems);

    saveToCrm(syncItems, "");
  };

  const saveToCrmFromTargetSelection = (uploadTargetId: string) => {
    saveToCrm(selections, uploadTargetId);
  };

  const saveToCrm = async (
    selections: Record<string, CRMSyncItemSelection> | undefined,
    uploadTargetId: string
  ) => {
    setIsSaving(true);

    try {
      const {
        success,
        displayTitle,
        displayDescription,
        userInputRequired,
        uploadTargetOptions,
        error,
      } = await fetchPost("/feapi/notes/sync-to-crm", {
        noteId: noteId,
        uploadTargetId,
        syncItems: selections,
      });

      // handle outright failures
      if (!success) {
        toast.error(`Failed to sync note to CRM. ${error}`, {
          autoClose: 2000,
          toastId: "sync-to-crm-error",
        });
        return;
      }

      // happy flow
      if (!userInputRequired) {
        closeAll();

        toast.success("Note synced to CRM", {
          autoClose: 2000,
          toastId: "sync-to-crm-success",
        });
        // Ensure toast is displayed before revalidating
        setTimeout(() => {
          revalidator.revalidate();
        }, 0);

        return;
      }

      // in case user input is required, open the target selection modal
      if (userInputRequired && uploadTargetOptions) {
        setSelectedModal(Modal.TargetSelection);
        setSyncTargets(uploadTargetOptions);
        setDisplayTitle(displayTitle);
        setDisplayDescription(displayDescription);
        return;
      }

      // There won't be any case where `userInputRequired` is true and `uploadTargetOptions` is undefined. Right? RIGHT??
      throw new Error("Unexpected response from server");
    } catch (e) {
      toast.error("Failed to sync note to CRM. Please try again", {
        autoClose: 2000,
        toastId: "sync-to-crm-error",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const selectionModalData = useMemo(
    () => getCardData(noteData, enableNoteTabsNameChanges),
    [noteData, enableNoteTabsNameChanges]
  );

  return (
    <>
      <Button
        onClick={onClickCta}
        variant="ghost"
        className=""
        data-onboarding="sync-crm-cta"
        aria-label="Sync to CRM"
        disabled={disabled}
      >
        <RefreshCcw />

        <span className="hidden sm:block">Sync to CRM</span>
      </Button>

      {selectedModal === Modal.ClientSelection && (
        <ClientSelectionModal
          onClose={closeAll}
          onContinue={saveClient}
          currentClient={currentClient}
          isSaving={isSaving}
        />
      )}

      {selectedModal === Modal.SelectionAndCertification && (
        <SyncDataSelectionModal
          selectionEnabled={syncSelectionEnabled ?? false}
          dataSet={selectionModalData}
          onClose={closeAll}
          onContinue={saveToCrmFromAttributeSelection}
          isSaving={isSaving}
          title={syncSelectionEnabled ? "Attributes to sync" : "Sync to CRM"}
        />
      )}

      {selectedModal === Modal.TargetSelection && (
        <TargetSelectionModal
          onClose={closeAll}
          onContinue={saveToCrmFromTargetSelection}
          targets={syncTargets}
          title={displayTitle || "Select CRM Upload Target"}
          description={displayDescription || "Upload target"}
          isSaving={isSaving}
        />
      )}
    </>
  );
};

export default SyncToCrmFlow;
