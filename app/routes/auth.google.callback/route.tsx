import { LoaderFunctionArgs, redirect } from "react-router";
import { setUpOAuthProvider } from "~/api/oauth/setUpOAuthProvider.server";
import { OAuthRequestProviderEnum } from "~/api/openapi/generated";
import { GOOGLE_STRATEGY, authenticator } from "~/auth/authenticator.server";
import { logError } from "~/utils/log.server";

// Exports
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const searchParams = new URL(request.url).searchParams;
  const state = searchParams.get("state");
  const isEmailIntegration = state === "email_integration";
  const isCalendarIntegration = state === "calendar_integration";

  if (isCalendarIntegration || isEmailIntegration) {
    const authorizationCode = searchParams.get("code");
    if (!authorizationCode) {
      throw new Error("Missing authorization_code query parameter");
    }

    try {
      const provider = isEmailIntegration
        ? OAuthRequestProviderEnum.Gmail
        : OAuthRequestProviderEnum.Google;

      await setUpOAuthProvider({
        authorizationCode,
        provider,
        request,
      });

      const integrationName = isEmailIntegration ? "Gmail" : "Google Calendar";
      return redirect(
        `/settings/integrations?type=integration&status=true&name=${integrationName}`
      );
    } catch (error) {
      const integrationName = isEmailIntegration ? "Gmail" : "Google Calendar";
      logError("Failed to setup ${integrationName} integration", error);
      return redirect(
        "/settings/integrations?type=integration&status=false&name=${integrationName}"
      );
    }
  }

  await authenticator.authenticate(GOOGLE_STRATEGY, request, {
    successRedirect: "/",
    failureRedirect: "/auth/login",
  });
  return null;
};
