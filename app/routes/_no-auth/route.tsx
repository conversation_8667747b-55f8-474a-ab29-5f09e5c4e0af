import { datadogLogs } from "@datadog/browser-logs";
import { datadogRum } from "@datadog/browser-rum";
import { useLoaderData, type LoaderFunctionArgs } from "react-router";
import { Outlet } from "react-router";
import { configurationForBrowser } from "~/api/openapi/browserConfigParams";
import { authenticator } from "~/auth/authenticator.server";
import { AuthContext } from "~/context/apiAuth";

// Exports
export const loader = async ({ request }: LoaderFunctionArgs) => {
  // On page load, check if user is already authenticated and redirect them to
  // /notes if they are already logged in
  await authenticator.isAuthenticated(request, {
    successRedirect: "/",
  });

  return { apiEndpoint: process.env.ZEPLYN_FRONTEND_API_BASE_URL };
};

const Route = () => {
  const { apiEndpoint } = useLoaderData<typeof loader>();
  datadogRum.clearUser();
  datadogLogs.clearUser();
  return (
    <AuthContext.Provider
      value={configurationForBrowser(undefined, apiEndpoint)}
    >
      <Outlet />
    </AuthContext.Provider>
  );
};
export default Route;
