import { ProcessingStatus } from "~/api/openapi/generated";

export default function getSearchParams(urlParams: { [key: string]: string }) {
  const searchTextParam = urlParams.q || "";

  const statusesParam =
    (urlParams.statuses?.split(",") as ProcessingStatus[]) || [];

  const selectedAttendeesParam = urlParams.attendees?.split(",") || [];

  return { searchTextParam, statusesParam, selectedAttendeesParam };
}
