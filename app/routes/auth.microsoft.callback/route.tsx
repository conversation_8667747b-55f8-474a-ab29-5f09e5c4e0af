import { LoaderFunctionArgs, redirect } from "react-router";
import { setUpOAuthProvider } from "~/api/oauth/setUpOAuthProvider.server";
import { OAuthRequestProviderEnum } from "~/api/openapi/generated";
import { MICROSOFT_STRATEGY, authenticator } from "~/auth/authenticator.server";
import { logError } from "~/utils/log.server";

// Exports
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const searchParams = new URL(request.url).searchParams;
  const state = searchParams.get("state");

  if (state === "calendar_integration" || state === "email_integration") {
    const authorizationCode = searchParams.get("code");
    if (!authorizationCode) {
      throw new Error("Missing authorization_code query parameter");
    }

    try {
      const provider =
        state === "email_integration"
          ? OAuthRequestProviderEnum.OutlookEmail
          : OAuthRequestProviderEnum.Microsoft;

      await setUpOAuthProvider({
        authorizationCode,
        provider,
        request,
      });

      const integrationName =
        state === "email_integration" ? "Outlook Email" : "Microsoft Calendar";
      return redirect(
        `/settings/integrations?type=integration&status=true&name=${integrationName}`
      );
    } catch (error) {
      const integrationName =
        state === "email_integration" ? "Outlook Email" : "Microsoft Calendar";
      logError(
        `Failed to setup ${integrationName.toLowerCase()} integration`,
        error
      );
      return redirect(
        `/settings/integrations?type=integration&status=false&name=${integrationName}`
      );
    }
  }

  const error = searchParams.get("error");
  if (
    process.env.ZEPLYN_ENABLE_MICROSOFT_LOGIN_AUTO_REDIRECT &&
    (error === "interaction_required" || error === "login_required")
  ) {
    return redirect("/auth/microsoft?prompt=select_account");
  }

  await authenticator.authenticate(MICROSOFT_STRATEGY, request, {
    successRedirect: "/",
    failureRedirect: "/auth/login",
  });
  return null;
};
