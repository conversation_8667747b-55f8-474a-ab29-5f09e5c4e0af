import { useState } from "react";
import { ListFilter, SearchIcon } from "lucide-react";

import { Button } from "~/@shadcn/ui/button";
import {
  Drawer,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "~/@shadcn/ui/drawer";
import { Label } from "~/@shadcn/ui/label";
import { MultiSelect } from "~/@ui/MultiSelect";
import { ProcessingStatus } from "~/api/openapi/generated";
import { useUserAgent } from "~/context/userAgent";
import { cn } from "~/@shadcn/utils";

type Props = {
  searchText: string;
  setSearchText: (searchText: string) => void;
  statuses: ProcessingStatus[];
  setStatuses: React.Dispatch<React.SetStateAction<ProcessingStatus[]>>;
  selectedAttendees: string[];
  setSelectedAttendees: React.Dispatch<React.SetStateAction<string[]>>;
  statusOptions: { label: string; value: ProcessingStatus }[];
  clientOptions: { label: string; value: string }[];
  onReset: () => void;
  onSearch: () => void;
};
const Search = ({
  searchText,
  setSearchText,
  statuses,
  setStatuses,
  selectedAttendees,
  setSelectedAttendees,
  statusOptions,
  clientOptions,
  onReset,
  onSearch,
}: Props) => {
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const { isMobile } = useUserAgent();

  if (isMobile) {
    let filterCount = 0;
    statuses.length && filterCount++;
    selectedAttendees.length && filterCount++;

    return (
      <div className="my-4 flex w-full max-w-[1100px] items-center gap-2 rounded-lg border shadow-md">
        {/* input bar */}
        <input
          type="text"
          placeholder="Type name or keywords…"
          className="grow rounded-lg p-3 text-sm outline-none"
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          autoFocus
        />

        {/* filter icon */}
        <button
          onClick={() => setIsFilterModalOpen(true)}
          className="relative inline-flex size-9 items-center justify-center"
        >
          <ListFilter size={18} />
          {!!filterCount && (
            <span className="absolute -right-1 -top-1 inline-flex size-5 items-center justify-center rounded-full text-xs font-semibold">
              {filterCount}
            </span>
          )}
        </button>

        {/* search CTA */}
        <button
          className="inline-flex h-full items-center rounded-r-lg bg-primary px-5 text-primary-foreground"
          onClick={onSearch}
        >
          <SearchIcon size={18} />
        </button>

        {/* filters modal */}
        <Drawer open={isFilterModalOpen} onOpenChange={setIsFilterModalOpen}>
          <DrawerContent>
            <DrawerHeader>
              <DrawerTitle>Search filters</DrawerTitle>
            </DrawerHeader>

            <div className="flex flex-col gap-4 p-4">
              <div className="flex flex-col gap-2">
                <Label>Status</Label>
                <MultiSelect
                  options={statusOptions}
                  selected={statuses}
                  onChange={(statuses) =>
                    setStatuses(statuses as ProcessingStatus[])
                  }
                  placeholder="Select Status"
                  triggerClassName="shadow-none w-fit"
                  useLabelForSearch
                  modal
                />
              </div>

              <div className="flex flex-col gap-2">
                <Label>Attendees</Label>
                <MultiSelect
                  options={clientOptions}
                  selected={selectedAttendees}
                  onChange={setSelectedAttendees}
                  placeholder="Select Attendees"
                  triggerClassName="shadow-none w-fit"
                  modal
                />
              </div>
            </div>

            <DrawerFooter className="flex flex-row justify-end gap-2">
              <Button variant="ghost" onClick={onReset}>
                Clear filters
              </Button>
              <Button
                variant="outline_primary"
                onClick={() => setIsFilterModalOpen(false)}
              >
                Done
              </Button>
            </DrawerFooter>
          </DrawerContent>
        </Drawer>
      </div>
    );
  }

  const shouldDisableSearch =
    !searchText && !statuses.length && !selectedAttendees.length;

  return (
    <div className="my-4 flex w-full max-w-[1100px] items-center gap-2 rounded-lg border shadow-lg focus-within:border-primary">
      <input
        type="text"
        placeholder="Type name or keywords to search…"
        className="grow rounded-lg p-4 outline-none"
        value={searchText}
        onChange={(e) => setSearchText(e.target.value)}
        onKeyDown={(e) => {
          if (e.key === "Enter") {
            !shouldDisableSearch && onSearch();
          }
        }}
        autoFocus
      />

      <MultiSelect
        options={statusOptions}
        selected={statuses}
        onChange={(statuses) => setStatuses(statuses as ProcessingStatus[])}
        placeholder="Select Status"
        triggerClassName="shadow-none border-none w-fit"
        useLabelForSearch
      />

      <MultiSelect
        options={clientOptions}
        selected={selectedAttendees}
        onChange={setSelectedAttendees}
        placeholder="Select Attendees"
        triggerClassName="shadow-none border-none w-fit"
      />

      <button
        className={cn(
          "inline-flex h-full items-center gap-2 rounded-r-lg bg-primary px-5 text-primary-foreground",
          shouldDisableSearch &&
            "pointer-events-none bg-muted text-muted-foreground"
        )}
        onClick={onSearch}
        disabled={shouldDisableSearch}
      >
        <SearchIcon size={18} />
      </button>
    </div>
  );
};

export default Search;
