import { ArrowLef<PERSON>, RotateCw } from "lucide-react";
import { isSameDay } from "date-fns";

import { ListNotesResponse, ProcessingStatus } from "~/api/openapi/generated";
import PastNoteCard from "./PastNoteCard";
import ScheduledCard from "./ScheduledCard";
import { Button } from "~/@shadcn/ui/button";
import { CombinedEvent, isNoteEmpty } from "~/utils/notesUtils";
import { useFlag } from "~/context/flags";

type Props = {
  isSearching?: boolean;
  filteredCalendarEvents: CombinedEvent[];
  filteredNotes: ListNotesResponse[];
  onReset: () => void;
  crmSystem: string | null | undefined;
  openDeleteModal: (noteId: string) => void;
};

const SearchResults = (props: Props) => {
  const {
    isSearching,
    filteredCalendarEvents,
    filteredNotes,
    onReset,
    crmSystem,
    openDeleteModal,
  } = props;

  const hasResults = !!filteredCalendarEvents.length || !!filteredNotes.length;

  const isSrpEnabled = useFlag("EnableSearchPage");

  return (
    <div className="w-full">
      <h2 className="flex items-center justify-between text-2xl font-light">
        {!isSrpEnabled && (
          <>
            <Button variant="ghost" onClick={onReset}>
              <ArrowLeft size={16} />
            </Button>

            <span className="ml-1 mr-auto">Search Results</span>
          </>
        )}

        {hasResults && (
          <Button variant="outline" onClick={onReset}>
            <RotateCw size={16} /> Clear all
          </Button>
        )}
      </h2>

      {!hasResults && isSearching && (
        <div className="text-center">
          No results found. Try{" "}
          <span onClick={onReset} className="cursor-pointer text-primary">
            resetting your search
          </span>
          .
        </div>
      )}

      {hasResults && (
        <div className="mt-4 grid gap-2">
          {filteredCalendarEvents.map((event) => {
            return (
              <ScheduledCard
                key={event.id}
                toPrep={event.toPrep}
                toMeeting={event.toMeeting}
                meetingName={event.meetingName}
                scheduledStartTime={new Date(event.startTime)}
                scheduledEndTime={new Date(event.endTime || "")}
                attendees={event.attendees ?? []}
                meetingType={event.meetingType || undefined}
                active={
                  isSameDay(new Date(event.startTime), new Date()) &&
                  new Date() >= new Date(event.startTime) &&
                  (!event.endTime || new Date() <= new Date(event.endTime))
                }
                status={event.status || ProcessingStatus.Scheduled}
                meetingLink={event.meetingLink || ""}
                autojoinEnabled={!!event.autojoinEnabled}
                autojoinAvailable={!!event.autojoinAvailable}
                scheduledEventUUID={event.scheduledEventUUID || ""}
              />
            );
          })}

          {filteredNotes.map((note) => {
            const { scheduledStartTime, scheduledEndTime, scheduledEventUuid } =
              note;

            // if the note has a corresponding event, skip it; this prevents duplication
            if (
              doesCorrespondingEventExist(
                scheduledEventUuid,
                filteredCalendarEvents
              )
            ) {
              return null;
            }

            if (note.status === "scheduled" && scheduledStartTime) {
              return (
                <ScheduledCard
                  key={note.uuid}
                  toPrep={{
                    pathname: `/notes/create/${note.uuid}`,
                    search: `?noteID=${note.uuid}&action=meeting-prep&tab=prep`,
                  }}
                  toMeeting={{
                    pathname: `/notes/create/${note.uuid}`,
                    search: `?noteID=${note.uuid}`,
                  }}
                  meetingName={note.meetingName}
                  scheduledStartTime={new Date(scheduledStartTime)}
                  scheduledEndTime={
                    scheduledEndTime ? new Date(scheduledEndTime) : undefined
                  }
                  attendees={note.attendees ?? []}
                  meetingType={note.meetingType || undefined}
                  active={
                    isSameDay(new Date(scheduledStartTime), new Date()) &&
                    new Date() >= new Date(scheduledStartTime) &&
                    (!scheduledEndTime ||
                      new Date() <= new Date(scheduledEndTime))
                  }
                  status={note.status}
                  meetingLink={note.meetingLink || ""}
                  autojoinEnabled={!!note.autojoinEnabled}
                  autojoinAvailable={!!note.autojoinAvailable}
                  scheduledEventUUID={note.scheduledEventUuid || ""}
                />
              );
            }

            return (
              <PastNoteCard
                key={note.uuid}
                noteId={note.uuid}
                client={note.client}
                meetingName={note.meetingName}
                meetingType={note.meetingType}
                date={new Date(note.created)}
                status={note.status}
                isEmpty={isNoteEmpty(note)}
                crmSystem={crmSystem}
                to={{ pathname: `/notes/${note.uuid}` }}
                onDelete={() => openDeleteModal(note.uuid)}
              />
            );
          })}
        </div>
      )}
    </div>
  );
};

function doesCorrespondingEventExist(
  scheduledEventUuid: string | null | undefined,
  filteredCalendarEvents: CombinedEvent[]
) {
  return filteredCalendarEvents.some(
    (event) => event.scheduledEventUUID === scheduledEventUuid
  );
}

export default SearchResults;
