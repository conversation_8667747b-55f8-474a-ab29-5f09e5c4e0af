import { useState } from "react";
import { useNavigate } from "react-router";

import SearchExperience from "./SearchExperience";
import { ListNotesResponse, ProcessingStatus } from "~/api/openapi/generated";

const SearchExperienceWrapper = ({ notes }: { notes: ListNotesResponse[] }) => {
  const [searchText, setSearchText] = useState("");
  const [statuses, setStatuses] = useState<ProcessingStatus[]>([]);
  const [selectedAttendees, setSelectedAttendees] = useState<string[]>([]);

  const navigate = useNavigate();

  // construct search page URL with query params, and redirect
  const onSearch = () => {
    const url = new URL(window.location.href);
    const params = new URLSearchParams(url.search);

    if (searchText) {
      params.set("q", searchText);
    } else {
      params.delete("q");
    }

    if (statuses.length) {
      params.set("statuses", statuses.join(","));
    } else {
      params.delete("statuses");
    }

    if (selectedAttendees.length) {
      params.set("attendees", selectedAttendees.join(","));
    } else {
      params.delete("attendees");
    }

    navigate({
      pathname: "/search",
      search: params.toString(),
    });
  };

  return (
    <SearchExperience
      notes={notes}
      crmSystem={null}
      openDeleteModal={() => {}}
      searchText={searchText}
      setSearchText={setSearchText}
      statuses={statuses}
      setStatuses={setStatuses}
      selectedAttendees={selectedAttendees}
      setSelectedAttendees={setSelectedAttendees}
      filteredCalendarEvents={[]}
      filteredNotes={[]}
      onClearSearch={() => {}}
      onSearch={onSearch}
    />
  );
};

export default SearchExperienceWrapper;
