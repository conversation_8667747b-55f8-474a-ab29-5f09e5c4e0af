import ScheduledCard from "./components/ScheduledCard";
import {
  MetaFunction,
  LoaderFunctionArgs,
  ActionFunction,
  ShouldRevalidateFunctionArgs,
} from "react-router";
import {
  useFetcher,
  useLoaderData,
  useNavigate,
  useLocation,
  NavLink,
  data,
} from "react-router";
import { addHours, isSameDay, subDays } from "date-fns";
import { useEffect, useMemo, useState } from "react";
import { toast } from "react-toastify";
import { Steps } from "intro.js-react";

import { ConfirmModal } from "~/@ui/ConfirmModal";
import { ContentV2, LayoutV2 } from "~/@ui/layout/LayoutV2";
import { getClients } from "~/api/notes/getClients.server";
import { getNotes } from "~/api/notes/getNotes.server";
import { configurationParameters } from "~/api/openapi/configParams";
import {
  Configuration,
  CalendarApi,
  ListNotesResponse,
  NoteApi,
  MeetingArtifactsApi,
  ProcessingStatus,
  ClientInteraction,
  ScheduledEvent,
  TeamsApi,
} from "~/api/openapi/generated";
import { getClientTimeZone } from "~/utils/hints";
import { logError } from "~/utils/log.server";
import { combineEvents, isNoteEmpty } from "~/utils/notesUtils";
import PastNoteCard from "./components/PastNoteCard";
import { Typography } from "~/@ui/Typography";
import { Fab } from "~/@ui/Fab";
import { Filter, Plus } from "lucide-react";
import { Skeleton } from "~/@shadcn/ui/skeleton";
import { Divider } from "~/@ui/Divider";
import OnboardingScheduledCard from "./components/OnboardingScheduledCard";
import { tutorialSteps } from "./utils";
import useOnboarding from "~/utils/useOnboarding";
import MeetingTabs from "./components/Tabs";
import { useFlag } from "~/context/flags";
import { useUserAgent } from "~/context/userAgent";
import SearchExperience from "./components/SearchExperience";
import SearchExperienceWrapper from "./components/SearchExperienceWrapper";
import getFilteredNotes from "~/routes/_auth.search/utils/getFilteredNotes";
import { MultiSelect } from "~/@ui/MultiSelect";
import { saveUiSettingsInCookies } from "~/utils/uiCookies";
import { useUiSettings } from "~/context/uiSettings";
import { useAPIConfiguration } from "~/context/apiAuth";

type FetcherData = { success?: boolean; error?: string };

export const loader = async ({ request }: LoaderFunctionArgs) => {
  // Fetch recent notes (from the last 3 days) separately from all notes.
  //
  // We pass the recent notes to the client to show immediately (which works well for the default
  // "today" tab), and pass the promise to a useEffect to resolve and update the notes list once all
  // notes have been fetched.
  const allNotesPromise = getNotes({ request }).catch(
    () => [] as ListNotesResponse[]
  );

  const recentNotesPromise = getNotes({
    request,
    notBefore: subDays(new Date(), 3).getTime() / 1000,
  }).catch(() => [] as ListNotesResponse[]);

  const config = new Configuration(await configurationParameters(request));
  const interactionsPromise = new MeetingArtifactsApi(config)
    .meetingArtifactsListClientInteractions()
    .catch(() => [] as ClientInteraction[]);

  const calendarEventsPromise = new CalendarApi(config)
    .calendarScheduledCalendarEvents(
      { timeZone: getClientTimeZone(request) },
      { signal: AbortSignal.timeout(5000) }
    )
    .catch(() => undefined);

  const clientPromise = getClients({ request, pageSize: 1 }).catch(() => ({
    crmSystem: undefined,
  }));

  const teamsPromise = new TeamsApi(config)
    .teamsListTeams()
    .catch(() => undefined);

  const [
    { crmSystem },
    recentNotesData,
    clientInteractionsData,
    calendarEvents,
    teams,
  ] = await Promise.all([
    clientPromise,
    recentNotesPromise,
    interactionsPromise,
    calendarEventsPromise,
    teamsPromise,
  ]);

  return {
    crmSystem,
    recentNotes: recentNotesData,
    allNotesPromise,
    clientInteractions: clientInteractionsData,
    calendarEvents,
    teams,
  };
};

export const action: ActionFunction = async ({ request }) => {
  try {
    const contentType = request.headers.get("content-type");
    if (!contentType || !contentType.includes("multipart/form-data")) {
      throw new Error("Unsupported content type");
    }

    const formData = await request.formData();
    const actionType = formData.get("actionType");
    const noteId = formData.get("noteId");

    if (typeof actionType !== "string") {
      return data(
        {
          errors: ["Invalid form data: actionType must be strings"],
        },
        { status: 400 }
      );
    }

    const configuration = new Configuration(
      await configurationParameters(request)
    );

    switch (actionType) {
      case "delete-note": {
        if (typeof noteId !== "string") {
          return data(
            { errors: ["Invalid form data: noteId must be a string"] },
            { status: 400 }
          );
        }
        await new NoteApi(configuration).noteDeleteNote({ noteId });
        return data({ success: true });
      }
      case "update-auto-join": {
        const autojoinEnabled = formData.get("autojoinEnabled");
        const scheduledEventUUID = formData.get("scheduledEventUUID");
        if (typeof scheduledEventUUID !== "string") {
          return data(
            {
              errors: [
                "Invalid form data: scheduledEventUUID must be a string",
              ],
            },
            { status: 400 }
          );
        }

        await new CalendarApi(configuration).calendarUpdateAutojoin({
          bodyCalendarUpdateAutojoin: {
            autoJoin: autojoinEnabled === "true",
            scheduledEventUuid: scheduledEventUUID as string,
          },
        });
        return data({ success: true });
      }

      default:
        return data(
          { errors: [`Unknown action type: ${actionType}`] },
          { status: 400 }
        );
    }
  } catch (error) {
    logError("app/routes/hub.tsx action", error);
    return data(
      { errors: ["Failed to update meeting due to server error"] },
      { status: 500 }
    );
  }
};

export const meta: MetaFunction = () => [
  { title: "Advisor Hub" },
  { name: "description", content: "View your hub" },
];

const AdvisorHub = () => {
  const {
    recentNotes,
    allNotesPromise,
    clientInteractions,
    calendarEvents,
    crmSystem,
    teams,
  } = useLoaderData<typeof loader>();
  const fetcher = useFetcher<FetcherData>();
  const navigate = useNavigate();
  const location = useLocation();
  const tabFromSearchParams =
    location.search && new URLSearchParams(location.search).get("tab");
  const [currentTab, setCurrentTab] = useState(
    tabFromSearchParams && tabFromSearchParams.length > 0
      ? tabFromSearchParams
      : "today"
  );
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [currentNote, setCurrentNote] = useState("");
  const { isTutorialEnabled, completeTutorial } = useOnboarding("dashboard", {
    triggerViaUrl: true,
  });

  // Populate the notes list with the recent notes (fully fetched in the loader).
  const [notes, setNotes] = useState<ListNotesResponse[]>(recentNotes);
  const [loadingMoreNotes, setLoadingMoreNotes] = useState(true);

  // When all notes have been fetched, update the notes list (and stop showing loading skeletons).
  useEffect(() => {
    allNotesPromise.then((allNotes) => {
      setLoadingMoreNotes(false);
      setNotes(allNotes);
    });
  }, [allNotesPromise]);

  const apiConfig = useAPIConfiguration();
  const noteApiClient = useMemo(() => new NoteApi(apiConfig), [apiConfig]);

  const uiSettings = useUiSettings();
  const [selectedTeams, setSelectedTeams] = useState<string[]>(
    uiSettings.defaultTeamUuidsForSearch || []
  ); // load from ui settings cookies

  const updateRecentNotes = async () => {
    console.log("selectedTeams", selectedTeams);
    try {
      const result = await noteApiClient.noteListNotes({
        includeTeams: selectedTeams.length ? selectedTeams : null,
      });
      setNotes(result);
    } catch (e) {
      console.log("error", e);
    }
  }

  const { isMobile } = useUserAgent();

  const isCollapsibleNavbarEnabled = useFlag("EnableCollapsibleNavbar");
  const isUnifyAdvisorHubAndNotesEnabled = useFlag(
    "EnableUnifyAdvisorHubAndNotes"
  );
  const isSrpEnabled = useFlag("EnableSearchPage");

  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      if (fetcher.data.success) {
        toast.update("delete-note", {
          render: "Note deleted",
          type: toast.TYPE.SUCCESS,
          isLoading: false,
          autoClose: 2000,
        });
      } else {
        toast.update("delete-note", {
          render: "Failed to delete note",
          type: toast.TYPE.SUCCESS,
          isLoading: false,
          autoClose: 2000,
        });
      }
    }
  }, [fetcher.state, fetcher.data]);

  const openDeleteModal = (uuid: string) => {
    setIsDeleteModalOpen(true);
    setCurrentNote(uuid);
  };

  const closeDeleteModal = () => setIsDeleteModalOpen(false);

  const handleDelete = () => {
    const formData = new FormData();
    formData.append("actionType", "delete-note");
    formData.append("noteId", currentNote);
    fetcher.submit(formData, {
      method: "post",
      encType: "multipart/form-data",
    });
    toast.loading("Deleting note", {
      position: "top-center",
      autoClose: 2000,
      hideProgressBar: true,
      closeOnClick: true,
      progress: undefined,
      toastId: "delete-note",
    });
  };

  const confirmDelete = () => {
    handleDelete();
    closeDeleteModal();
  };

  const groupNotesByTime = (notes: ListNotesResponse[]) => {
    const finishedToday: ListNotesResponse[] = [];
    const past: ListNotesResponse[] = [];
    const upcoming: ListNotesResponse[] = [];

    const now = new Date();

    notes.forEach((note) => {
      // TODO: always use the note's scheduled end time
      const referenceDate =
        note.status == ProcessingStatus.Scheduled
          ? note.scheduledEndTime
            ? new Date(note.scheduledEndTime)
            : addHours(new Date(note.scheduledStartTime ?? note.created), 1)
          : new Date(note.created);
      if (referenceDate > now) {
        upcoming.push(note);
      } else if (isSameDay(referenceDate, now)) {
        finishedToday.push(note);
      } else {
        past.push(note);
      }
    });

    return { upcoming, finishedToday, past };
  };

  const { upcoming, finishedToday, past } = groupNotesByTime(notes);

  const renderNotesSection = (
    calendarEvents: ScheduledEvent[],
    upcomingNotes: ListNotesResponse[],
    todaysFinishedNotes: ListNotesResponse[],
    pastNotes: ListNotesResponse[],
    loadingMoreNotes: boolean
  ) => {
    // We need to handle all meeting source IDs; if a calendar event moved, we don't want it to show
    // up again in the upcoming section if there is an associated note.
    const meetingSourceIDs = new Set(
      upcomingNotes
        .concat(pastNotes)
        .concat(todaysFinishedNotes)
        .map((note) => note.meetingSourceId)
        .filter((id): id is string => id !== null)
    );

    const currentTabIsToday = currentTab === "today";

    const combinedEvents = combineEvents(
      calendarEvents,
      upcomingNotes,
      clientInteractions,
      meetingSourceIDs,
      currentTabIsToday
    );

    // in case of onboarding flow, render the mock card
    if (isTutorialEnabled) {
      return <OnboardingScheduledCard />;
    }

    if (currentTab === "past") {
      console.log("pastNotes", pastNotes.length)
      return (
        <>
          {pastNotes
            .filter((note) => note.status !== ProcessingStatus.Unknown)
            .map((note, index) => (
              <PastNoteCard
                key={note.uuid}
                // key={`${note.uuid}_${index}`} // NOT
                noteId={note.uuid}
                client={note.client}
                meetingName={note.meetingName}
                meetingType={note.meetingType}
                date={new Date(note.created)}
                status={note.status}
                isEmpty={isNoteEmpty(note)}
                crmSystem={crmSystem}
                to={{ pathname: `/notes/${note.uuid}` }}
                onDelete={() => openDeleteModal(note.uuid)}
              />
            ))}
          {loadingMoreNotes && (
            <>
              <Skeleton className="h-36 w-full rounded-md" />
              <Skeleton className="h-36 w-full rounded-md" />
              <Skeleton className="h-36 w-full rounded-md" />
              <Skeleton className="h-36 w-full rounded-md" />
              <Skeleton className="h-36 w-full rounded-md" />
              <Skeleton className="h-36 w-full rounded-md" />
            </>
          )}
        </>
      );
    }

    // TODO: @debojyotighosh Errors during hydration owing to mismatch of attendee.uuid values (inside event.toPrep & event.toMeeting)
    return (
      <>
        {combinedEvents.length > 0 ? (
          combinedEvents.map((event) => (
            <ScheduledCard
              key={event.id}
              toPrep={event.toPrep}
              toMeeting={event.toMeeting}
              meetingName={event.meetingName}
              scheduledStartTime={new Date(event.startTime)}
              scheduledEndTime={
                event.endTime ? new Date(event.endTime) : undefined
              }
              interaction={event.interaction}
              attendees={event.attendees ?? []}
              meetingType={event.meetingType || undefined}
              active={
                isSameDay(new Date(event.startTime), new Date()) &&
                new Date() >= new Date(event.startTime) &&
                (!event.endTime || new Date() <= new Date(event.endTime))
              }
              status={event.status || ProcessingStatus.Scheduled}
              meetingLink={event.meetingLink}
              autojoinAvailable={event.autojoinAvailable}
              autojoinEnabled={event.autojoinEnabled}
              scheduledEventUUID={event.scheduledEventUUID}
            />
          ))
        ) : (
          <Typography variant="h3">
            🎉 No more meetings{currentTabIsToday ? " today" : ""}!
          </Typography>
        )}
        {currentTab === "today" && todaysFinishedNotes.length > 0 && (
          <Divider />
        )}
        {currentTab === "today" &&
          todaysFinishedNotes.map((note) => (
            <PastNoteCard
              key={note.uuid}
              noteId={note.uuid}
              client={note.client}
              meetingName={note.meetingName}
              meetingType={note.meetingType}
              date={new Date(note.created)}
              status={note.status}
              isEmpty={isNoteEmpty(note)}
              crmSystem={crmSystem}
              to={{ pathname: `/notes/${note.uuid}` }}
              onDelete={() => openDeleteModal(note.uuid)}
            />
          ))}
      </>
    );
  };

  const onTabChange = (newValue: string) => {
    setCurrentTab(newValue);
    navigate({ search: `tab=${newValue}` }, { replace: true });
  };

  const onCompleteTutorial = () => {
    completeTutorial();
    navigate("/dashboard", { replace: true });
  };

  const onExitTutorial = (stepNumber: number) => {
    // check to avoid the issue (potentially with React wrapper itself) where onExitTutorial is called automatically at the beginning
    if (stepNumber >= 0) {
      onCompleteTutorial();
    }
  };

  const [searchText, setSearchText] = useState("");
  const [statuses, setStatuses] = useState<ProcessingStatus[]>([]);
  const [selectedAttendees, setSelectedAttendees] = useState<string[]>([]);

  const combinedEvents = useMemo(
    () => combineEvents(calendarEvents || [], [], [], new Set()),
    [calendarEvents]
  );

  // generate search results if any of the params exist
  const { filteredCalendarEvents, filteredNotes } = !(
    searchText ||
    statuses.length ||
    selectedAttendees.length
  )
    ? { filteredCalendarEvents: [], filteredNotes: [] }
    : getFilteredNotes(
        notes,
        combinedEvents,
        searchText,
        statuses,
        selectedAttendees
      );

  // show search results if any of the params exist; this logic is used only when `EnableSearchPage` flag is disabled
  const isShowingSearchResults = !!(
    searchText ||
    statuses.length ||
    selectedAttendees.length
  );

  const changeTeams: React.Dispatch<React.SetStateAction<string[]>> = (
    teams
  ) => {
    // save data in cookies
    saveUiSettingsInCookies({
      ...uiSettings,
      defaultTeamUuidsForSearch: teams,
    });

    // update local storage
    setSelectedTeams(teams);
  };

  return (
    <LayoutV2>
      <ContentV2
        floatingAction={
          <Fab asChild>
            <NavLink to="/notes/create">
              <Plus />
            </NavLink>
          </Fab>
        }
        className="w-80 min-w-80"
      >
        <Steps
          enabled={isTutorialEnabled}
          steps={tutorialSteps(!!isCollapsibleNavbarEnabled)}
          initialStep={0}
          onExit={onExitTutorial}
          onComplete={onCompleteTutorial}
          options={{
            exitOnOverlayClick: false,
          }}
        />

        <div className="sm:mb-0_ mb-32 flex w-full flex-col items-center gap-4">
          {isUnifyAdvisorHubAndNotesEnabled && !isSrpEnabled && (
            <SearchExperience
              isShowingSearchResults={isShowingSearchResults}
              notes={notes}
              crmSystem={crmSystem}
              openDeleteModal={openDeleteModal}
              searchText={searchText}
              setSearchText={setSearchText}
              statuses={statuses}
              setStatuses={setStatuses}
              selectedAttendees={selectedAttendees}
              setSelectedAttendees={setSelectedAttendees}
              filteredCalendarEvents={filteredCalendarEvents}
              filteredNotes={filteredNotes}
              onClearSearch={() => {
                setSearchText("");
                setStatuses([]);
                setSelectedAttendees([]);
              }}
              onSearch={() => {}}
            />
          )}

          {isUnifyAdvisorHubAndNotesEnabled && isSrpEnabled && (
            <SearchExperienceWrapper notes={notes} />
          )}

          meow1

          {!isShowingSearchResults && (
            <>
              <MeetingTabs onChange={onTabChange} value={currentTab} />
              {calendarEvents ? (
                renderNotesSection(
                  calendarEvents.filter((e) => !!e),
                  upcoming,
                  finishedToday,
                  [...finishedToday, ...past],
                  loadingMoreNotes
                )
              ) : (
                <>
                  <Typography color="error">
                    Error fetching calendar events.
                  </Typography>
                  {renderNotesSection(
                    [],
                    upcoming,
                    finishedToday,
                    [...finishedToday, ...past],
                    loadingMoreNotes
                  )}
                </>
              )}
            </>
          )}

          meow2
        </div>

        {/* TODO: @debojyotighosh Implement responsive UI */}
        {/* Teams filter (desktop only - for now) */}
        {!isMobile && !!teams?.teams?.length && (
          <div className="fixed bottom-5 left-[50%] flex translate-x-[-50%] items-center gap-4 rounded-full border border-gray-200 bg-white/75 px-8 py-4 shadow-lg backdrop-blur-sm">
            <Filter size={14} />
            <div className="-ml-2 text-sm text-muted-foreground">Team View</div>

            <MultiSelect
              options={teams.teams.map(({ name, uuid }) => ({
                label: name,
                value: uuid,
              }))}
              selected={selectedTeams}
              onChange={changeTeams}
              placeholder="Select Teams"
              triggerClassName="shadow-none w-fit"
            />

            <button onClick={updateRecentNotes}>Go!</button>
          </div>
        )}

        <ConfirmModal
          isOpen={isDeleteModalOpen}
          onClose={closeDeleteModal}
          onConfirm={confirmDelete}
          title="Confirm Delete"
          message="Are you sure you want to delete this note? This will delete the note for all users it's shared with."
        />
      </ContentV2>
    </LayoutV2>
  );
};

export default AdvisorHub;

export function shouldRevalidate({
  currentUrl,
  nextUrl,
  defaultShouldRevalidate,
}: ShouldRevalidateFunctionArgs) {
  if (currentUrl.pathname !== nextUrl.pathname) {
    return defaultShouldRevalidate;
  }

  // If all that changed in the search params is the `tab`, we don't need to rerun the loader.
  const currentWithoutTab = new URLSearchParams(currentUrl.searchParams);
  currentWithoutTab.delete("tab");

  const nextWithoutTab = new URLSearchParams(nextUrl.searchParams);
  nextWithoutTab.delete("tab");

  if (currentWithoutTab.toString() === nextWithoutTab.toString()) {
    return false;
  }
  return defaultShouldRevalidate;
}
