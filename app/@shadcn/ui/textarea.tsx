import * as React from "react";
import { useEffect, useRef } from "react";
import { VariantProps, cva } from "class-variance-authority";
import { cn } from "~/@shadcn/utils";

// Constants
export const textareaVariants = cva(
  "cursor-text transition-colors read-only:pointer-events-none hover:border-foreground disabled:cursor-not-allowed disabled:opacity-50",
  {
    variants: {
      variant: {
        default:
          "border-b border-border bg-background focus:border-b focus:border-primary focus:outline-none",
        outline:
          "rounded-2xl border border-border bg-background p-3 shadow hover:bg-accent focus:outline-none focus:ring-2 focus:ring-ring",
      },
      typographyVariant: {
        default: "text-base font-normal",
        body2: "text-sm font-normal",
        h1: "text-3xl font-semibold",
        h2: "text-2xl font-normal",
        h3: "text-xl font-semibold leading-9",
        h4: "text-base font-semibold leading-5",
        h5: "text-base font-semibold",
        h6: "text-base font-semibold",
      },
      color: {
        default: "text-foreground placeholder:text-secondary",
        primary: "text-foreground placeholder:text-secondary",
        secondary: "text-secondary placeholder:text-secondary",
        error: "text-destructive placeholder:text-destructive-muted",
        warning: "text-warning placeholder:text-secondary",
        success: "text-success placeholder:text-muted-foreground",
      },
    },
    defaultVariants: {
      variant: "default",
      typographyVariant: "default",
      color: "default",
    },
  }
);

// Helpers
const addTrailingSpace = (val: string) =>
  val.charAt(val.length - 1) === "\n" ? `${val} ` : val;

// Exports
export type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement> &
  VariantProps<typeof textareaVariants> & {
    resizable?: boolean;
  };

export const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      className,
      variant,
      typographyVariant,
      color,
      resizable = false,
      ...props
    },
    ref
  ) => (
    <textarea
      className={cn(
        "flex w-full items-center",
        textareaVariants({ variant, typographyVariant, color }),
        resizable ? "resize-y" : "resize-none",
        className
      )}
      ref={ref}
      {...props}
    />
  )
);
Textarea.displayName = "Textarea";

/**
 * This is a <textarea /> component that grows/shrinks in height automatically
 * based on user input. It is based on the approach described in this blog post:
 * https://css-tricks.com/the-cleanest-trick-for-autogrowing-textareas/
 */
export const TextareaGrowable = React.forwardRef<
  HTMLTextAreaElement,
  TextareaProps & { isVisible?: boolean }
>(
  (
    {
      className,
      variant,
      typographyVariant,
      color,
      resizable = false,
      disabled,
      isVisible,
      ...props
    },
    ref
  ) => {
    //Default ref value when no ref is passed
    const internalRef = useRef<HTMLTextAreaElement>(null);
    const growerRef = useRef<HTMLDivElement>(null);

    const combinedRef = ref || internalRef;

    const adjustHeight = () => {
      const textarea = (combinedRef as React.RefObject<HTMLTextAreaElement>)
        .current;
      if (textarea) {
        textarea.style.height = "auto";
        textarea.style.height = `${textarea.scrollHeight}px`;
      }
    };

    useEffect(() => {
      if (growerRef.current) {
        growerRef.current.textContent = addTrailingSpace(
          String(props.defaultValue || "")
        );
      }
    }, [props.defaultValue]);

    useEffect(() => {
      adjustHeight();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [props.value, disabled, isVisible]);

    const handleInput = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
      if (growerRef.current) {
        growerRef.current.textContent = addTrailingSpace(
          event.currentTarget.value
        );
      }
      adjustHeight(); // Adjust height on input
      if (props.onInput) {
        props.onInput(event);
      }
    };

    return (
      <div className="relative grid max-h-[50vh] w-full">
        <textarea
          ref={combinedRef}
          rows={1}
          style={{ gridArea: "1 / 1 / 2 / 2", minHeight: "10vh" }}
          className={cn(
            "max-h-[50vh] min-h-[10vh] resize-none overflow-hidden overflow-y-auto break-words",
            textareaVariants({ variant, typographyVariant, color }),
            className
          )}
          onInput={handleInput}
          disabled={disabled}
          {...props}
        />
        <div
          ref={growerRef}
          aria-hidden="true"
          style={{
            gridArea: "1 / 1 / 2 / 2",
            wordBreak: "break-all",
            overflowWrap: "break-word",
            whiteSpace: "pre-wrap",
            visibility: "hidden",
            minHeight: "1.5em",
          }}
          className={cn(
            "invisible whitespace-pre-wrap",
            textareaVariants({ variant, typographyVariant, color }),
            className
          )}
        />
      </div>
    );
  }
);

TextareaGrowable.displayName = "TextareaGrowable";
